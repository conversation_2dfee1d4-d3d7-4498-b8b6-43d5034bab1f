const express = require("express");
const router = express.Router();

// Import route modules
const authRoutes = require("./auth.routes");
const tenantRoutes = require("./tenant.routes");
const vatRateRoutes = require("./vat-rate.routes");
const productDiscountRoutes = require("./product-discount.routes");
const bulkDiscountRoutes = require("./bulk-discount.routes");
const inventoryItemRoutes = require("./inventory-item.routes");
const soldBarcodeRoutes = require("./sold-barcode.routes");
const inventoryTransactionRoutes = require("./inventory-transaction.routes");
const inventoryExcelRoutes = require("./inventory-excel.routes");
const priceListExcelRoutes = require("./price-list-excel.routes");
const invoiceRoutes = require('./invoice.routes');
const creditNoteRoutes = require('./credit-note.routes');
const timsLogRoutes = require('./tims-log.routes');
const corporateSalesRoutes = require('./corporate-sales.routes');

// Import schema for product categories
require("./product-category.schema");

// Import schemas for Swagger documentation
require("./tenant.schema");
require("./stock-item.schema");
require("./product.schema");
require("./brand.schema");
require("./brand-type.schema");
require("./stockAdjustmentType.schema");
require("./stockAdjustmentItem.schema");
require("./stockAdjustment.schema");
require("./mpesaFloatReconciliation.schema");
require("./stockMovement.schema");
require("./stock-request.schema");
require("./cash-transaction.schema");
require("./employee.schema");
require("./vat-rate.schema");
require("./product-discount.schema");
require("./bulk-discount.schema");
require("./inventory-item.schema");
require("./inventory-excel.schema");

require("./mpesaFloatMovement.schema");
require("./dsa-stock.schema");
require("./dsa-customer.schema");
require("./branch.schema");
require("./region.schema");
require("./customer.schema");
require("./sale.schema");
require("./phone-repair.schema");
require("./pos-session.schema");
require("./banking-transaction.schema");
const branchRoutes = require("./branch.routes");
const userRoutes = require("./user.routes");
const productRoutes = require("./product.routes");
const stockItemRoutes = require("./stock-item.routes");
const bankRoutes = require("./bank.routes");

const stockMovementRoutes = require("./stock-movement.routes");
const stockMovementReportRoutes = require("./stock-movement-report.routes");
const stockRequestRoutes = require("./stock-request.routes");
const mpesaFloatMovementRoutes = require("./mpesa-float-movement.routes");
const mpesaFloatTopupRoutes = require("./mpesa-float-topup.routes");
const testRoutes = require("./test.routes");
// DSA routes
const dsaStockAssignmentRoutes = require("./dsa-stock-assignment.routes");
const dsaCustomerRoutes = require("./dsa-customer.routes");
const dsaStockReconciliationRoutes = require("./dsa-stock-reconciliation.routes");
const dsaStockRoutes = require("./dsa-stock.routes");
const dsaPaymentRoutes = require("./dsa-payment.routes");
const dsaInvoiceRoutes = require("./dsa-invoice.routes");
const dsaEnhancedRoutes = require("./dsa-enhanced.routes");
// const mpesaFloatAssignmentRoutes = require('./mpesaFloatAssignment.routes'); - Removed
const mpesaFloatBalanceRoutes = require("./mpesaFloatBalance.routes");
const mpesaTransactionRoutes = require("./mpesa-transaction.routes");
const mpesaTransactionAuditRoutes = require("./mpesa-transaction-audit.routes");
const mpesaFloatReconciliationRoutes = require("./mpesa-float-reconciliation.routes");
const posSessionRoutes = require("./pos-session.routes");
const posSessionReconciliationRoutes = require("./pos-session-reconciliation.routes");
const saleRoutes = require("./sale.routes");
const saleItemRoutes = require("./sale-item.routes");
const paymentMethodRoutes = require("./payment-method.routes");
const locationRoutes = require("./location.routes");
const customerRoutes = require("./customer.routes");
const phoneRepairRoutes = require("./phone-repair.routes");
const roleRoutes = require("./role.routes");
const permissionRoutes = require("./permission.routes");
const rbacRoutes = require("./rbac.routes");
const stockAdjustmentRoutes = require("./stockAdjustment.routes");
const productCategoryRoutes = require("./product-category.routes");
const regionRoutes = require("./region.routes");
const bankingRoutes = require("./banking.routes");
const brandRoutes = require("./brand.routes");
const brandTypeRoutes = require("./brand-type.routes");
const cashTransactionRoutes = require("./cash-transaction.routes");
const employeeRoutes = require("./employee.routes");
const creditPartnerRoutes = require("./credit-partner.routes");
const expenseRoutes = require("./expense.routes");
const expenseCategoryRoutes = require("./expense-category.routes");
const expenseCategoryGroupRoutes = require("./expense-category-group.routes");
const expenseApprovalRoutes = require("./expense-approval.routes");
const expenseAnalyticsRoutes = require("./expense-analytics.routes");
const purchaseRoutes = require("./purchase.routes");
const purchaseReturnRoutes = require("./purchase-return.routes");
const supplierRoutes = require("./supplier.routes");
const procurementRoutes = require("./procurement.routes");
const reportRoutes = require("./report/index");

// Mount route groups
router.use("/auth", authRoutes);
router.use("/tenants", tenantRoutes);
router.use("/branches", branchRoutes);
router.use("/users", userRoutes);
router.use("/products", productRoutes);
router.use("/stock-items", stockItemRoutes);

router.use("/stock-movements", stockMovementRoutes);
router.use("/stock-movement-reports", stockMovementReportRoutes);
router.use("/stock-requests", stockRequestRoutes);
router.use("/mpesa-float-movements", mpesaFloatMovementRoutes);
router.use("/mpesa-float-topups", mpesaFloatTopupRoutes);
router.use("/test", testRoutes);
// DSA routes
router.use("/dsa-stock-assignments", dsaStockAssignmentRoutes);
router.use("/dsa-customers", dsaCustomerRoutes);
router.use("/dsa-stock-reconciliations", dsaStockReconciliationRoutes);
router.use("/dsa-stock", dsaStockRoutes);
router.use("/dsa-payments", dsaPaymentRoutes);
router.use("/dsa-invoices", dsaInvoiceRoutes);
router.use("/dsa-enhanced", dsaEnhancedRoutes);
// router.use('/mpesa-float', mpesaFloatAssignmentRoutes); - Removed
router.use("/mpesa-float-balances", mpesaFloatBalanceRoutes);
router.use("/mpesa-transactions", mpesaTransactionRoutes);
router.use("/mpesa-transaction-audits", mpesaTransactionAuditRoutes);
router.use("/mpesa-float-reconciliations", mpesaFloatReconciliationRoutes);
router.use("/pos-sessions", posSessionRoutes);
router.use("/pos-session-reconciliations", posSessionReconciliationRoutes);
router.use("/sales", saleRoutes);
router.use("/sale-items", saleItemRoutes);
router.use("/payment-methods", paymentMethodRoutes);
router.use("/locations", locationRoutes);
router.use("/customers", customerRoutes);
router.use("/phone-repairs", phoneRepairRoutes);
router.use("/roles", roleRoutes);
router.use("/permissions", permissionRoutes);
router.use("/rbac", rbacRoutes);
router.use("/stock-adjustments", stockAdjustmentRoutes);
router.use("/product-categories", productCategoryRoutes);
router.use("/regions", regionRoutes);
router.use("/banking", bankingRoutes);
router.use("/banks", bankRoutes);
router.use("/brands", brandRoutes);
router.use("/brand-types", brandTypeRoutes);
router.use("/cash-transactions", cashTransactionRoutes);
router.use("/employees", employeeRoutes);
router.use("/credit-partners", creditPartnerRoutes);
router.use("/expenses", expenseRoutes);
router.use("/expense-categories", expenseCategoryRoutes);
router.use("/expense-category-groups", expenseCategoryGroupRoutes);
// Expense approval routes are included in the expense routes
router.use("/expense-analytics", expenseAnalyticsRoutes);
router.use("/vat-rates", vatRateRoutes);
router.use("/inventory-items", inventoryItemRoutes);
router.use("/inventory-excel", inventoryExcelRoutes);
router.use("/price-list-excel", priceListExcelRoutes);
router.use("/sold-barcodes", soldBarcodeRoutes);
router.use("/purchases", purchaseRoutes);
router.use("/purchase-returns", purchaseReturnRoutes);
router.use("/suppliers", supplierRoutes);
router.use("/procurement", procurementRoutes);
router.use("/reports", reportRoutes);
router.use("/", productDiscountRoutes);
router.use("/", bulkDiscountRoutes);
router.use("/", inventoryTransactionRoutes);
router.use('/invoices', invoiceRoutes);
router.use('/credit-notes', creditNoteRoutes);
router.use('/tims-logs', timsLogRoutes);
router.use('/corporate-sales', corporateSalesRoutes);

// Root route
router.get("/", (_req, res) => {
  res.json({
    message: "Welcome to the POS API",
    version: "1.0.0",
  });
});

module.exports = router;
