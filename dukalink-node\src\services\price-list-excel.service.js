/**
 * Price List Excel Service
 * Handles Excel operations for price list management
 */
const ExcelJS = require("exceljs");
const XLSX = require("xlsx");
const { Product, StockItem } = require("../models");
const logger = require("../utils/logger");
const sequelize = require("../../config/database");
const AppError = require("../utils/error");

/**
 * Generate Excel template for price list import
 * @param {Object} options - Options for template generation
 * @param {number} options.tenantId - Tenant ID to filter products
 * @returns {Buffer} Excel file buffer
 */
const generatePriceListImportTemplate = async ({ tenantId }) => {
  try {
    logger.info(`Generating price list template for tenant: ${tenantId}`);

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = "DukaLink POS";
    workbook.created = new Date();
    workbook.modified = new Date();

    // Add a worksheet for the template
    const worksheet = workbook.addWorksheet("Price List Template");

    // Define columns
    worksheet.columns = [
      { header: "Product*", key: "product_id", width: 40 },
      { header: "Cost Price*", key: "cost_price", width: 15 },
      { header: "Selling Price*", key: "selling_price", width: 15 },
      { header: "Margin", key: "margin", width: 15 },
      { header: "Margin Percentage", key: "margin_percentage", width: 20 },
    ];

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFD3D3D3" },
    };

    // Add a hidden sheet for products dropdown
    const productsDropdownSheet = workbook.addWorksheet("ProductsDropdown");
    productsDropdownSheet.hidden = true;
    productsDropdownSheet.columns = [
      { header: "Product", key: "product", width: 40 },
    ];

    // Add instructions in a separate worksheet
    const instructionsSheet = workbook.addWorksheet("Instructions");
    instructionsSheet.columns = [
      { header: "Field", key: "field", width: 25 },
      { header: "Description", key: "description", width: 70 },
      { header: "Required", key: "required", width: 15 },
    ];

    // Style the header row
    instructionsSheet.getRow(1).font = { bold: true };
    instructionsSheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFD3D3D3" },
    };

    // Add instructions
    instructionsSheet.addRows([
      {
        field: "Product",
        description:
          "Select a product from the dropdown list. The system will automatically use the product ID.",
        required: "Yes",
      },
      {
        field: "Cost Price",
        description:
          "The buying/cost price of the product. Must be a positive number.",
        required: "Yes",
      },
      {
        field: "Selling Price",
        description:
          "The selling price of the product. Must be a positive number.",
        required: "Yes",
      },
      {
        field: "Margin",
        description:
          "Calculated automatically as (Selling Price - Cost Price). You can also enter manually.",
        required: "No",
      },
      {
        field: "Margin Percentage",
        description:
          "Margin as percentage of cost price: ((Selling Price - Cost Price) / Cost Price) * 100. Enter as number (e.g., 30 for 30%).",
        required: "No",
      },
    ]);

    // Fetch all active products for the tenant
    const products = await Product.findAll({
      where: {
        tenant_id: tenantId,
        deleted_at: null,
      },
      attributes: ['id', 'name', 'sku'],
      order: [['name', 'ASC']],
    });

    logger.info(`Found ${products.length} products for template`);

    // Add a worksheet with available products
    const productsSheet = workbook.addWorksheet("Available Products");
    productsSheet.columns = [
      { header: "Product ID", key: "id", width: 15 },
      { header: "Name", key: "name", width: 40 },
      { header: "SKU", key: "sku", width: 20 },
    ];

    // Style the header row
    productsSheet.getRow(1).font = { bold: true };
    productsSheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFD3D3D3" },
    };

    // Add products to the sheet
    productsSheet.addRows(
      products.map((p) => ({
        id: p.id,
        name: p.name,
        sku: p.sku || '',
      }))
    );

    // Add products to the dropdown sheet
    const productOptions = products.map((p) => ({
      product: `${p.name} (${p.sku || "No SKU"}) - ID: ${p.id}`,
    }));
    productsDropdownSheet.addRows(productOptions);

    // Add data validation for the Product column (dropdown)
    if (products.length > 0) {
      worksheet.dataValidations.add('A2:A1000', {
        type: 'list',
        allowBlank: false,
        formulae: [`=ProductsDropdown!$A$2:$A$${products.length + 1}`]
      });
    }

    // Add example data with the first product if available
    if (products.length > 0) {
      const exampleProduct = products[0];
      worksheet.addRow({
        product_id: `${exampleProduct.name} (${exampleProduct.sku || "No SKU"}) - ID: ${exampleProduct.id}`,
        cost_price: '',
        selling_price: '',
        margin: '',
        margin_percentage: '',
      });
    }

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    logger.info("Price list template generated successfully");

    return buffer;
  } catch (error) {
    logger.error(`Error generating price list template: ${error.message}`);
    throw new AppError("Failed to generate price list template", 500);
  }
};

/**
 * Parse price list Excel file
 * @param {string|Buffer} fileData - File path or buffer
 * @param {Object} options - Parsing options
 * @param {boolean} options.useFilePath - Whether fileData is a file path
 * @returns {Object} Parsed data with rows
 */
const parsePriceListExcel = async (fileData, { useFilePath = false } = {}) => {
  let workbook;
  let worksheet;

  try {
    // First try with ExcelJS
    workbook = new ExcelJS.Workbook();

    if (useFilePath) {
      await workbook.xlsx.readFile(fileData);
    } else {
      await workbook.xlsx.load(fileData);
    }

    worksheet = workbook.getWorksheet(1);
  } catch (error) {
    // If ExcelJS fails, try with XLSX library
    logger.info(`ExcelJS failed, trying with XLSX library: ${error.message}`);

    try {
      if (useFilePath) {
        workbook = XLSX.readFile(fileData);
      } else {
        workbook = XLSX.read(fileData, { type: "buffer" });
      }

      const firstSheetName = workbook.SheetNames[0];
      const xlsxWorksheet = workbook.Sheets[firstSheetName];
      const jsonData = XLSX.utils.sheet_to_json(xlsxWorksheet, { header: 1 });

      // Create a compatible worksheet object
      worksheet = {
        getRow: (rowNumber) => {
          const row = jsonData[rowNumber - 1] || [];
          return {
            eachCell: (callback) => {
              row.forEach((cellValue, index) => {
                callback({ value: cellValue }, index + 1);
              });
            },
            getCell: (cellNumber) => {
              return { value: row[cellNumber - 1] };
            },
          };
        },
        get rowCount() {
          return jsonData.length;
        },
      };
    } catch (xlsxError) {
      logger.error(`Both ExcelJS and XLSX failed: ${xlsxError.message}`);
      throw new AppError("Unable to parse Excel file. Please ensure it's a valid Excel file.", 400);
    }
  }

  if (!worksheet) {
    throw new AppError("No worksheet found in the Excel file", 400);
  }

  const rows = [];
  const headerRow = worksheet.getRow(1);
  const headers = [];

  // Extract header values
  const headerValues = [];
  headerRow.eachCell((cell) => {
    headerValues.push(cell.value);
  });

  logger.info(`Headers found: ${JSON.stringify(headerValues)}`);

  // Map column indices using exact header matching (like inventory service)
  const columnIndices = {};
  headerValues.forEach((header, index) => {
    if (header === "Product*" || header === "Product") {
      columnIndices.product = index;
    }
    if (header === "Cost Price*" || header === "Cost Price") {
      columnIndices.costPrice = index;
    }
    if (header === "Selling Price*" || header === "Selling Price") {
      columnIndices.sellingPrice = index;
    }
    if (header === "Margin") {
      columnIndices.margin = index;
    }
    if (header === "Margin Percentage") {
      columnIndices.marginPercentage = index;
    }
  });

  // Validate that required columns were found
  if (columnIndices.product === undefined) {
    throw new AppError("Product column not found in the Excel file", 400);
  }
  if (columnIndices.costPrice === undefined) {
    throw new AppError("Cost Price column not found in the Excel file", 400);
  }
  if (columnIndices.sellingPrice === undefined) {
    throw new AppError("Selling Price column not found in the Excel file", 400);
  }

  logger.info(`Column indices mapped:`, columnIndices);

  // Process data rows (skip header row)
  for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
    const row = worksheet.getRow(rowNumber);
    const rowData = [];

    // Extract all cell values from the row
    row.eachCell((cell) => {
      rowData.push(cell.value);
    });

    // Skip empty rows
    if (
      rowData.length === 0 ||
      rowData.every((cell) => cell === null || cell === undefined || cell === "")
    ) {
      continue;
    }

    // Extract data using column indices
    let productText = rowData[columnIndices.product];
    const costPrice = rowData[columnIndices.costPrice];
    const sellingPrice = rowData[columnIndices.sellingPrice];
    const margin = columnIndices.margin !== undefined ? rowData[columnIndices.margin] : null;
    const marginPercentage = columnIndices.marginPercentage !== undefined ? rowData[columnIndices.marginPercentage] : null;

    logger.debug(`Row ${rowNumber} raw data:`, {
      productText,
      costPrice,
      sellingPrice,
      margin,
      marginPercentage
    });

    // Extract product ID from dropdown text (same logic as inventory service)
    let productId = null;
    if (typeof productText === "string") {
      // Try exact ID format first (e.g., "Product Name (SKU) - ID: 123")
      let match = productText.match(/ID:\s*(\d+)/);
      if (match && match[1]) {
        productId = parseInt(match[1], 10);
        logger.debug(`Extracted product ID ${productId} from dropdown value using ID: pattern`);
      }
      // Try to extract just numbers if that fails
      else {
        match = productText.match(/(\d+)/);
        if (match && match[1]) {
          productId = parseInt(match[1], 10);
          logger.debug(`Extracted product ID ${productId} from dropdown value using numeric pattern`);
        } else {
          logger.warn(`Could not extract product ID from value: "${productText}"`);
        }
      }
    } else if (typeof productText === "number") {
      productId = productText;
      logger.debug(`Product ID provided as number: ${productId}`);
    }

    const processedRowData = {
      rowNumber,
      productId,
      costPrice: costPrice ? parseFloat(costPrice) : null,
      sellingPrice: sellingPrice ? parseFloat(sellingPrice) : null,
      margin: margin ? parseFloat(margin) : null,
      marginPercentage: marginPercentage ? parseFloat(marginPercentage) : null,
    };

    logger.debug(`Row ${rowNumber} processed data:`, processedRowData);
    rows.push(processedRowData);
  }

  logger.info(`Parsed ${rows.length} data rows from price list Excel file`);

  return { rows };
};

/**
 * Process price list import data
 * @param {string|Buffer} fileData - File path or buffer
 * @param {Object} options - Processing options
 * @param {number} options.tenantId - Tenant ID
 * @param {number} options.userId - User ID for audit trail
 * @param {boolean} options.useFilePath - Whether fileData is a file path
 * @returns {Object} Processing results
 */
const processPriceListImport = async (fileData, { tenantId, userId, useFilePath = false }) => {
  const transaction = await sequelize.transaction();

  try {
    logger.info(`Processing price list import for tenant: ${tenantId}`);

    // Parse the Excel file
    const { rows } = await parsePriceListExcel(fileData, { useFilePath });

    const results = {
      success_count: 0,
      error_count: 0,
      errors: [],
      warnings: [],
    };

    // Process each row
    for (const row of rows) {
      try {
        logger.debug(`Processing row ${row.rowNumber}:`, row);

        // Validate required fields
        if (!row.productId) {
          logger.warn(`Row ${row.rowNumber}: Product ID is missing or invalid`);
          results.errors.push({
            row: row.rowNumber,
            message: "Product ID is required",
            data: row,
          });
          results.error_count++;
          continue;
        }

        if (row.costPrice === null || row.costPrice < 0) {
          logger.warn(`Row ${row.rowNumber}: Cost Price validation failed - value: ${row.costPrice}`);
          results.errors.push({
            row: row.rowNumber,
            message: "Cost Price must be a positive number",
            data: row,
          });
          results.error_count++;
          continue;
        }

        if (row.sellingPrice === null || row.sellingPrice < 0) {
          logger.warn(`Row ${row.rowNumber}: Selling Price validation failed - value: ${row.sellingPrice}`);
          results.errors.push({
            row: row.rowNumber,
            message: "Selling Price must be a positive number",
            data: row,
          });
          results.error_count++;
          continue;
        }

        // Find the product
        const product = await Product.findOne({
          where: {
            id: row.productId,
            tenant_id: tenantId,
            deleted_at: null,
          },
          transaction,
        });

        if (!product) {
          results.errors.push({
            row: row.rowNumber,
            message: `Product with ID ${row.productId} not found`,
            data: row,
          });
          results.error_count++;
          continue;
        }

        // Calculate margin percentage if not provided
        // Formula: ((Selling Price - Cost Price) / Cost Price) * 100
        let marginPercentage = row.marginPercentage;
        if (marginPercentage === null && row.costPrice > 0) {
          const margin = row.sellingPrice - row.costPrice;
          marginPercentage = (margin / row.costPrice) * 100;
        }

        // Update product pricing
        await product.update({
          suggested_buying_price: row.costPrice,
          suggested_selling_price: row.sellingPrice,
          retail_margin_percentage: marginPercentage || 0,
          last_updated_by: userId,
        }, { transaction });

        // Update all stock items for this product
        await StockItem.update({
          default_buying_price: row.costPrice,
          default_selling_price: row.sellingPrice,
          last_updated_by: userId,
        }, {
          where: {
            product_id: row.productId,
            tenant_id: tenantId,
            deleted_at: null,
          },
          transaction,
        });

        results.success_count++;
        logger.debug(`Updated pricing for product ${row.productId}`);

      } catch (rowError) {
        logger.error(`Error processing row ${row.rowNumber}: ${rowError.message}`);
        results.errors.push({
          row: row.rowNumber,
          message: rowError.message,
          data: row,
        });
        results.error_count++;
      }
    }

    await transaction.commit();

    logger.info(`Price list import completed: ${results.success_count} success, ${results.error_count} errors`);

    return results;
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error processing price list import: ${error.message}`);
    throw error;
  }
};

/**
 * Export current price list data to Excel
 * @param {Object} options - Export options
 * @param {number} options.tenantId - Tenant ID to filter products
 * @returns {Buffer} Excel file buffer
 */
const exportPriceListData = async ({ tenantId }) => {
  try {
    logger.info(`Exporting price list data for tenant: ${tenantId}`);

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Price List Export");

    // Define headers
    const headers = [
      "Product ID",
      "Product Name",
      "SKU",
      "Cost Price",
      "Selling Price",
      "Margin",
      "Margin Percentage"
    ];

    // Add headers
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Fetch all active products with their pricing
    const products = await Product.findAll({
      where: {
        tenant_id: tenantId,
        deleted_at: null,
      },
      attributes: [
        'id',
        'name',
        'sku',
        'suggested_buying_price',
        'suggested_selling_price',
        'retail_margin_percentage'
      ],
      order: [['name', 'ASC']],
    });

    // Add product data
    products.forEach((product) => {
      const costPrice = parseFloat(product.suggested_buying_price || 0);
      const sellingPrice = parseFloat(product.suggested_selling_price || 0);
      const margin = sellingPrice - costPrice;
      const marginPercentage = parseFloat(product.retail_margin_percentage || 0);

      worksheet.addRow([
        product.id,
        product.name,
        product.sku || '',
        costPrice,
        sellingPrice,
        margin,
        marginPercentage
      ]);
    });

    // Auto-fit columns
    worksheet.columns.forEach((column) => {
      let maxLength = 0;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const columnLength = cell.value ? cell.value.toString().length : 10;
        if (columnLength > maxLength) {
          maxLength = columnLength;
        }
      });
      column.width = Math.min(maxLength + 2, 50);
    });

    const buffer = await workbook.xlsx.writeBuffer();
    logger.info(`Price list export completed: ${products.length} products`);

    return buffer;
  } catch (error) {
    logger.error(`Error exporting price list data: ${error.message}`);
    throw new AppError("Failed to export price list data", 500);
  }
};

module.exports = {
  generatePriceListImportTemplate,
  parsePriceListExcel,
  processPriceListImport,
  exportPriceListData,
};
