"use client";

import { MainLayout } from "@/components/layouts/main-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ChevronRight, FileSpreadsheet } from "lucide-react";
import { PriceListTemplateDownload } from "@/features/inventory/components/price-list-template-download";
import { PriceListExcelUpload } from "@/features/inventory/components/price-list-excel-upload";
import { useExportAllPriceList } from "@/features/inventory/hooks/use-price-list-excel";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";

export default function PriceListPage() {
  // Use the export hook
  const {
    exportAll,
    isLoading: isExportLoading,
    productCount,
  } = useExportAllPriceList();

  // Handle export button click
  const handleExport = () => {
    if (isExportLoading) {
      toast.info("Export is already in progress");
      return;
    }

    if (productCount === 0) {
      toast.warning("No product data to export", {
        description: "Please add products before exporting",
      });
      return;
    }

    // Show a loading toast
    const loadingToastId = toast.loading(
      "Preparing price list data for export..."
    );

    // Start the export process
    const success = exportAll();

    // Dismiss the loading toast
    setTimeout(() => {
      toast.dismiss(loadingToastId);

      if (success) {
        toast.success("Price list exported successfully", {
          description: `${productCount} products exported to Excel`,
        });
      }
    }, 1000);
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/inventory">Inventory</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbLink>Price List Management</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Price List Management
          </h1>
          <p className="text-muted-foreground">
            Import and export product pricing data using Excel files
          </p>
        </div>

        <Tabs defaultValue="import" className="space-y-4">
          <TabsList>
            <TabsTrigger value="import">Import Price List</TabsTrigger>
            <TabsTrigger value="export">Export Price List</TabsTrigger>
          </TabsList>

          <TabsContent value="import" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Import Price List from Excel</CardTitle>
                <CardDescription>
                  Upload an Excel file to update product pricing data in bulk
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4">
                  Use this feature to import product pricing data from an Excel file.
                  The file should follow the template format and include product IDs,
                  cost prices, selling prices, and margin percentages.
                </p>

                <PriceListTemplateDownload />
                <div className="mt-8">
                  <PriceListExcelUpload />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Export Price List to Excel</CardTitle>
                <CardDescription>
                  Download current product pricing data as an Excel file
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4">
                  Use this feature to export your current product pricing data to an Excel
                  file for analysis, review, or record-keeping.
                </p>

                <div className="space-y-4">
                  <p>
                    You can export your current product pricing data to an Excel file.
                    This will include all products with their current cost prices,
                    selling prices, margins, and margin percentages.
                  </p>

                  {productCount > 0 && (
                    <p className="text-sm text-muted-foreground">
                      {productCount} products available for export
                    </p>
                  )}

                  <div className="flex justify-center">
                    <Button
                      onClick={handleExport}
                      disabled={isExportLoading}
                      className="w-full sm:w-auto"
                    >
                      {isExportLoading ? (
                        "Exporting..."
                      ) : (
                        <>
                          <FileSpreadsheet className="mr-2 h-4 w-4" /> Export
                          Price List to Excel
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
