{"/_not-found/page": "app/_not-found/page.js", "/banking/page": "app/banking/page.js", "/brands/page": "app/brands/page.js", "/credit-notes/new/page": "app/credit-notes/new/page.js", "/credit-notes/page": "app/credit-notes/page.js", "/customers/page": "app/customers/page.js", "/dashboard/page": "app/dashboard/page.js", "/expense-analytics/page": "app/expense-analytics/page.js", "/expenses/categories/page": "app/expenses/categories/page.js", "/expenses/page": "app/expenses/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/forgot-password/page": "app/forgot-password/page.js", "/inventory/excel/page": "app/inventory/excel/page.js", "/inventory/page": "app/inventory/page.js", "/inventory/price-list/page": "app/inventory/price-list/page.js", "/inventory/stock-cards/page": "app/inventory/stock-cards/page.js", "/invoices/[id]/page": "app/invoices/[id]/page.js", "/invoices/corporate-sales/new/page": "app/invoices/corporate-sales/new/page.js", "/invoices/new/page": "app/invoices/new/page.js", "/invoices/page": "app/invoices/page.js", "/login/page": "app/login/page.js", "/page": "app/page.js", "/pos/sessions/page": "app/pos/sessions/page.js", "/reports/banking-transactions/page": "app/reports/banking-transactions/page.js", "/reports/cash-status/page": "app/reports/cash-status/page.js", "/reports/current-stock-levels/page": "app/reports/current-stock-levels/page.js", "/reports/mpesa-transactions/page": "app/reports/mpesa-transactions/page.js", "/reports/sales-by-item/page": "app/reports/sales-by-item/page.js", "/reports/sales-summary/page": "app/reports/sales-summary/page.js", "/reports/shifts/page": "app/reports/shifts/page.js", "/reports/tax-report/page": "app/reports/tax-report/page.js", "/sales/page": "app/sales/page.js"}