{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/hooks/use-permissions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCurrentUser } from \"./use-auth\";\r\nimport { usePermissionContext } from \"../context/permission-context\";\r\n\r\n// Define constants for resources and actions\r\nexport const RESOURCES = {\r\n  USERS: \"users\",\r\n  ROLES: \"roles\",\r\n  PERMISSIONS: \"permissions\",\r\n  PRODUCTS: \"products\",\r\n  CATEGORIES: \"categories\",\r\n  BRANDS: \"brands\",\r\n  INVENTORY: \"inventory\",\r\n  STOCK_ITEMS: \"stock_items\",\r\n  BRANCHES: \"branches\",\r\n  EMPLOYEES: \"employees\",\r\n  BANKING: \"banking\",\r\n  EXPENSES: \"expenses\",\r\n  EXPENSE_FIRST_APPROVAL: \"expense_first_approval\",\r\n  EXPENSE_SECOND_APPROVAL: \"expense_second_approval\",\r\n  SALES: \"sales\",\r\n  POS_SESSIONS: \"pos_sessions\",\r\n  CUSTOMERS: \"customers\",\r\n  PROFILE: \"profile\",\r\n  STOCK_LOCATIONS: \"stock_locations\",\r\n};\r\n\r\nexport const ACTIONS = {\r\n  CREATE: \"create\",\r\n  READ: \"read\",\r\n  UPDATE: \"update\",\r\n  DELETE: \"delete\",\r\n};\r\n\r\nexport const SCOPE = {\r\n  ANY: \"any\",\r\n  OWN: \"own\",\r\n};\r\n\r\nexport function usePermissions() {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const {\r\n    permissions,\r\n    isLoading: isPermissionsLoading,\r\n    hasPermission,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n  } = usePermissionContext();\r\n\r\n  // Check if the user is a company admin\r\n  const isCompanyAdmin = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the company_admin role\r\n    return (\r\n      user.role_name === \"company_admin\" ||\r\n      user.role_name === \"tenant_admin\" ||\r\n      user.role_name === \"super_admin\"\r\n    );\r\n  };\r\n\r\n  // Check if the user is a branch manager\r\n  const isBranchManager = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the branch_manager role\r\n    return user.role_name === \"branch_manager\";\r\n  };\r\n\r\n  // Check if the user can manage stock locations (create, update, delete)\r\n  const canManageStockLocations = (): boolean => {\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.CREATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.UPDATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.DELETE, SCOPE.ANY) ||\r\n      isCompanyAdmin()\r\n    );\r\n  };\r\n\r\n  // Check if the user can view stock locations\r\n  const canViewStockLocations = (): boolean => {\r\n    // Check for explicit permission or fallback to any authenticated user\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.READ, SCOPE.ANY) ||\r\n      !!user\r\n    );\r\n  };\r\n\r\n  // Check if the user can perform an action on a resource\r\n  const can = (\r\n    resource: string,\r\n    action: string,\r\n    scope: \"any\" | \"own\" = \"any\"\r\n  ): boolean => {\r\n    return hasPermission(resource, action, scope);\r\n  };\r\n\r\n  // Check if the user has any of the specified permissions\r\n  const hasAnyPermission = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.some(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  // Check if the user has all of the specified permissions\r\n  const hasAllPermissions = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.every(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  return {\r\n    isCompanyAdmin,\r\n    isBranchManager,\r\n    canManageStockLocations,\r\n    canViewStockLocations,\r\n    can,\r\n    hasPermission,\r\n    hasAnyPermission,\r\n    hasAllPermissions,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n    permissions, // Expose the raw permissions for debugging\r\n    isLoading: isUserLoading || isPermissionsLoading,\r\n    RESOURCES,\r\n    ACTIONS,\r\n    SCOPE,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;AAHA;;;AAMO,MAAM,YAAY;IACvB,OAAO;IACP,OAAO;IACP,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,aAAa;IACb,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;IACV,wBAAwB;IACxB,yBAAyB;IACzB,OAAO;IACP,cAAc;IACd,WAAW;IACX,SAAS;IACT,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,QAAQ;IACnB,KAAK;IACL,KAAK;AACP;AAEO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EACJ,WAAW,EACX,WAAW,oBAAoB,EAC/B,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,+JAAA,CAAA,uBAAoB,AAAD;IAEvB,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,+CAA+C;QAC/C,OACE,KAAK,SAAS,KAAK,mBACnB,KAAK,SAAS,KAAK,kBACnB,KAAK,SAAS,KAAK;IAEvB;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAElB,gDAAgD;QAChD,OAAO,KAAK,SAAS,KAAK;IAC5B;IAEA,wEAAwE;IACxE,MAAM,0BAA0B;QAC9B,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE;IAEJ;IAEA,6CAA6C;IAC7C,MAAM,wBAAwB;QAC5B,sEAAsE;QACtE,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,KAChE,CAAC,CAAC;IAEN;IAEA,wDAAwD;IACxD,MAAM,MAAM,CACV,UACA,QACA,QAAuB,KAAK;QAE5B,OAAO,cAAc,UAAU,QAAQ;IACzC;IAEA,yDAAyD;IACzD,MAAM,mBAAmB,CACvB;QAMA,OAAO,YAAY,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC1D,cAAc,UAAU,QAAQ;IAEpC;IAEA,yDAAyD;IACzD,MAAM,oBAAoB,CACxB;QAMA,OAAO,YAAY,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC3D,cAAc,UAAU,QAAQ;IAEpC;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,iBAAiB;QAC5B;QACA;QACA;IACF;AACF;GArGgB;;QACmC,kJAAA,CAAA,iBAAc;QAO3D,+JAAA,CAAA,uBAAoB", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/route-permissions.ts"], "sourcesContent": ["/**\r\n * Route Permission Mapping\r\n *\r\n * This file maps routes to the permissions required to access them.\r\n * Each key represents a route pattern, and the value is the permission required.\r\n */\r\n\r\nimport { NavigationPermission } from \"./navigation-permissions\";\r\n\r\nexport const routePermissions: Record<string, NavigationPermission> = {\r\n  // Dashboard routes\r\n  \"/dashboard\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/company\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/branch\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/finance\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/operations\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/stock\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/float\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/pos\": { resource: \"dashboard\", action: \"view\" },\r\n\r\n  // User management routes\r\n  \"/users\": { resource: \"users\", action: \"view\" },\r\n  \"/users/create\": { resource: \"users\", action: \"create\" },\r\n  \"/users/[id]\": { resource: \"users\", action: \"view\" },\r\n  \"/users/[id]/edit\": { resource: \"users\", action: \"update\" },\r\n\r\n  // Role management routes\r\n  \"/roles\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/create\": { resource: \"roles\", action: \"create\" },\r\n  \"/roles/[id]\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/[id]/edit\": { resource: \"roles\", action: \"update\" },\r\n\r\n  // RBAC routes\r\n  \"/rbac\": { resource: \"permissions\", action: \"manage\" },\r\n\r\n  // Branch management routes\r\n  \"/branches\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/create\": { resource: \"branches\", action: \"create\" },\r\n  \"/branches/[id]\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/[id]/edit\": { resource: \"branches\", action: \"update\" },\r\n\r\n  // Location management routes\r\n  \"/locations\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/locations/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n  \"/location-guides\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/location-guides/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n\r\n  // Employee management routes\r\n  \"/employees\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/create\": { resource: \"employees\", action: \"create\" },\r\n  \"/employees/[id]\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/[id]/edit\": { resource: \"employees\", action: \"update\" },\r\n\r\n  // Product management routes\r\n  \"/products\": { resource: \"products\", action: \"view\" },\r\n  \"/products/create\": { resource: \"products\", action: \"create\" },\r\n  \"/products/[id]\": { resource: \"products\", action: \"view\" },\r\n  \"/products/[id]/edit\": { resource: \"products\", action: \"update\" },\r\n\r\n  // Category management routes\r\n  \"/categories\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/create\": { resource: \"categories\", action: \"create\" },\r\n  \"/categories/[id]\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/[id]/edit\": { resource: \"categories\", action: \"update\" },\r\n\r\n  // Brand management routes\r\n  \"/brands\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/create\": { resource: \"brands\", action: \"create\" },\r\n  \"/brands/[id]\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/[id]/edit\": { resource: \"brands\", action: \"update\" },\r\n\r\n  // Inventory management routes\r\n  \"/inventory\": { resource: \"inventory\", action: \"view\" },\r\n  \"/inventory/transfers\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/bulk-transfer\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/reports\": { resource: \"inventory\", action: \"report\" },\r\n  \"/inventory/stock-cards\": { resource: \"inventory\", action: \"view\" },\r\n\r\n  // Banking routes\r\n  \"/banking\": { resource: \"banking\", action: \"view\" },\r\n  \"/banking/summary\": { resource: \"banking\", action: \"view\" },\r\n\r\n  // MPESA routes\r\n  \"/mpesa\": { resource: \"mpesa\", action: \"view\" },\r\n  \"/mpesa/transactions\": { resource: \"mpesa\", action: \"view\" },\r\n\r\n  // Float management routes\r\n  \"/float\": { resource: \"float\", action: \"view\" },\r\n  \"/float/movements\": { resource: \"float\", action: \"view\" },\r\n  \"/float/reconciliations\": { resource: \"float\", action: \"view\" },\r\n  \"/float/topups\": { resource: \"float\", action: \"view\" },\r\n\r\n  // DSA routes\r\n  \"/dsa\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/agents\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/assignments\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/reconciliations\": { resource: \"dsa\", action: \"view\" },\r\n\r\n  // Phone repairs routes\r\n  \"/phone-repairs\": { resource: \"phone_repairs\", action: \"view\" },\r\n  \"/phone-repairs/[id]\": { resource: \"phone_repairs\", action: \"view\" },\r\n\r\n  // Expense management routes\r\n  \"/expenses\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/create\": { resource: \"expenses\", action: \"create\" },\r\n  \"/expenses/[id]\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/[id]/edit\": { resource: \"expenses\", action: \"update\" },\r\n  \"/expense-analytics\": { resource: \"expenses\", action: \"view\" },\r\n\r\n  // Credit Note routes\r\n  \"/credit-notes\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/new\": { resource: \"invoices\", action: \"create\" },\r\n  \"/credit-notes/[id]\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/[id]/edit\": { resource: \"invoices\", action: \"update\" },\r\n\r\n  // Report routes\r\n  \"/reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-summary\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-item\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-category\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-employee\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-payment-type\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-banking\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/cash-status\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/running-balances\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-transactions\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/phone-repairs\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/expense-reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/dsa-sales\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/receipts\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/shifts\": { resource: \"reports\", action: \"view\" },\r\n\r\n  // Settings routes\r\n  \"/settings\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/system\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/company\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/payment-methods\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/health\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // Profile route\r\n  \"/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // POS routes\r\n  \"/pos\": { resource: \"pos\", action: \"view\" },\r\n  \"/pos/sessions\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]/edit\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/close\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/shift-closing\": {\r\n    resource: \"pos_sessions\",\r\n    action: \"view\",\r\n  },\r\n\r\n  // Sales routes\r\n  \"/sales\": { resource: \"sales\", action: \"view\" },\r\n  \"/sales/[id]\": { resource: \"sales\", action: \"view\" },\r\n\r\n  // Customer routes\r\n  \"/customers\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/create\": { resource: \"customers\", action: \"create\" },\r\n  \"/customers/[id]\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/[id]/edit\": { resource: \"customers\", action: \"update\" },\r\n\r\n  // Procurement routes\r\n  \"/procurement\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests/new\": { resource: \"procurement\", action: \"create\" },\r\n  \"/procurement/receipts\": { resource: \"procurement\", action: \"view\" },\r\n\r\n  // Supplier routes\r\n  \"/suppliers\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/create\": { resource: \"suppliers\", action: \"create\" },\r\n  \"/suppliers/[id]\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/[id]/edit\": { resource: \"suppliers\", action: \"update\" },\r\n\r\n  // Tenant routes (Super Admin only)\r\n  \"/tenants\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/create\": { resource: \"tenants\", action: \"create\" },\r\n  \"/tenants/[id]\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/[id]/edit\": { resource: \"tenants\", action: \"update\" },\r\n\r\n  // Super Admin routes\r\n  \"/super-admin\": { resource: \"tenants\", action: \"view\" },\r\n\r\n  // Debug routes\r\n  \"/permission-debug\": { resource: \"settings\", action: \"manage\" },\r\n};\r\n\r\n/**\r\n * Get the permission required for a route\r\n * @param route The route to check\r\n * @returns The permission required for the route, or null if no permission is required\r\n */\r\nexport function getRoutePermission(route: string): NavigationPermission | null {\r\n  // First try exact match\r\n  if (routePermissions[route]) {\r\n    return routePermissions[route];\r\n  }\r\n\r\n  // Then try dynamic routes\r\n  for (const [pattern, permission] of Object.entries(routePermissions)) {\r\n    if (pattern.includes(\"[\") && matchDynamicRoute(route, pattern)) {\r\n      return permission;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Check if a route matches a dynamic route pattern\r\n * @param route The route to check\r\n * @param pattern The pattern to match against\r\n * @returns Whether the route matches the pattern\r\n */\r\nfunction matchDynamicRoute(route: string, pattern: string): boolean {\r\n  const routeParts = route.split(\"/\");\r\n  const patternParts = pattern.split(\"/\");\r\n\r\n  if (routeParts.length !== patternParts.length) {\r\n    return false;\r\n  }\r\n\r\n  for (let i = 0; i < patternParts.length; i++) {\r\n    if (patternParts[i].startsWith(\"[\") && patternParts[i].endsWith(\"]\")) {\r\n      // This is a dynamic part, so it matches anything\r\n      continue;\r\n    }\r\n\r\n    if (patternParts[i] !== routeParts[i]) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAIM,MAAM,mBAAyD;IACpE,mBAAmB;IACnB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC7D,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,kBAAkB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,cAAc;IACd,SAAS;QAAE,UAAU;QAAe,QAAQ;IAAS;IAErD,2BAA2B;IAC3B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAClE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,2BAA2B;QAAE,UAAU;QAAa,QAAQ;IAAS;IACrE,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,8BAA8B;QAAE,UAAU;QAAa,QAAQ;IAAS;IAExE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,eAAe;QAAE,UAAU;QAAc,QAAQ;IAAO;IACxD,sBAAsB;QAAE,UAAU;QAAc,QAAQ;IAAS;IACjE,oBAAoB;QAAE,UAAU;QAAc,QAAQ;IAAO;IAC7D,yBAAyB;QAAE,UAAU;QAAc,QAAQ;IAAS;IAEpE,0BAA0B;IAC1B,WAAW;QAAE,UAAU;QAAU,QAAQ;IAAO;IAChD,kBAAkB;QAAE,UAAU;QAAU,QAAQ;IAAS;IACzD,gBAAgB;QAAE,UAAU;QAAU,QAAQ;IAAO;IACrD,qBAAqB;QAAE,UAAU;QAAU,QAAQ;IAAS;IAE5D,8BAA8B;IAC9B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAW;IACpE,4BAA4B;QAAE,UAAU;QAAa,QAAQ;IAAW;IACxE,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAChE,0BAA0B;QAAE,UAAU;QAAa,QAAQ;IAAO;IAElE,iBAAiB;IACjB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,oBAAoB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE1D,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,uBAAuB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAE3D,0BAA0B;IAC1B,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAO;IACxD,0BAA0B;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9D,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAErD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,eAAe;QAAE,UAAU;QAAO,QAAQ;IAAO;IACjD,oBAAoB;QAAE,UAAU;QAAO,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAO,QAAQ;IAAO;IAE1D,uBAAuB;IACvB,kBAAkB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAC9D,uBAAuB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAEnE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAChE,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAE7D,qBAAqB;IACrB,iBAAiB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACxD,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC7D,2BAA2B;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEpE,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,kCAAkC;QAAE,UAAU;QAAW,QAAQ;IAAO;IACxE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,wBAAwB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC9D,6BAA6B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACnE,+BAA+B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACrE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,4BAA4B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClE,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC5D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC3D,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEzD,kBAAkB;IAClB,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,6BAA6B;QAAE,UAAU;QAAY,QAAQ;IAAS;IACtE,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC3D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE3D,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAElD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,iBAAiB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IAC5D,sBAAsB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IACjE,2BAA2B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACxE,4BAA4B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACzE,oCAAoC;QAClC,UAAU;QACV,QAAQ;IACV;IAEA,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IAEnD,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAC1D,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IACnE,6BAA6B;QAAE,UAAU;QAAe,QAAQ;IAAS;IACzE,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAEnE,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,mCAAmC;IACnC,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAC3D,iBAAiB;QAAE,UAAU;QAAW,QAAQ;IAAO;IACvD,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAE9D,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEtD,eAAe;IACf,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;AAChE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,wBAAwB;IACxB,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,OAAO,gBAAgB,CAAC,MAAM;IAChC;IAEA,0BAA0B;IAC1B,KAAK,MAAM,CAAC,SAAS,WAAW,IAAI,OAAO,OAAO,CAAC,kBAAmB;QACpE,IAAI,QAAQ,QAAQ,CAAC,QAAQ,kBAAkB,OAAO,UAAU;YAC9D,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,KAAa,EAAE,OAAe;IACvD,MAAM,aAAa,MAAM,KAAK,CAAC;IAC/B,MAAM,eAAe,QAAQ,KAAK,CAAC;IAEnC,IAAI,WAAW,MAAM,KAAK,aAAa,MAAM,EAAE;QAC7C,OAAO;IACT;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,IAAI,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM;YAEpE;QACF;QAEA,IAAI,YAAY,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/role-guard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect, useState } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\nimport { getRoutePermission } from \"@/lib/route-permissions\";\r\nimport { hasRouteAccess } from \"@/lib/role-utils\"; // Keep for backward compatibility\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\nfunction mapActionToGrantAction(action: string): string {\r\n  switch (action) {\r\n    case \"view\":\r\n      return \"read:any\";\r\n    case \"create\":\r\n      return \"create:any\";\r\n    case \"update\":\r\n      return \"update:any\";\r\n    case \"delete\":\r\n      return \"delete:any\";\r\n    case \"manage\":\r\n      return \"update:any\"; // Manage maps to update:any\r\n    case \"transfer\":\r\n      return \"update:any\"; // Transfer maps to update:any\r\n    case \"report\":\r\n      return \"read:any\"; // Report maps to read:any\r\n    default:\r\n      return `${action}:any`;\r\n  }\r\n}\r\n\r\ninterface RoleGuardProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function RoleGuard({ children }: RoleGuardProps) {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const { hasPermission, isLoading: isPermissionsLoading } = usePermissions();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Track if this is the initial mount\r\n  const [isInitialMount, setIsInitialMount] = useState(true);\r\n\r\n  // Effect to mark initial mount as complete\r\n  useEffect(() => {\r\n    setIsInitialMount(false);\r\n  }, []);\r\n\r\n  // Effect to check access rights\r\n  useEffect(() => {\r\n    // Skip check if on auth pages\r\n    if (\r\n      pathname.startsWith(\"/login\") ||\r\n      pathname.startsWith(\"/forgot-password\") ||\r\n      pathname.startsWith(\"/reset-password\")\r\n    ) {\r\n      setLoading(\"role\", false);\r\n      return;\r\n    }\r\n\r\n    // Only show loading indicator on initial mount or when user data is loading\r\n    // and we haven't shown the loading screen before in this session\r\n    if (isInitialMount && isUserLoading && !hasShownLoading.role) {\r\n      setLoading(\"role\", true);\r\n      return;\r\n    } else {\r\n      setLoading(\"role\", false);\r\n    }\r\n\r\n    // If user data and permissions are loaded, check access rights\r\n    if (!isUserLoading && !isPermissionsLoading && user) {\r\n      // Safely access role_name with type checking\r\n      const roleName =\r\n        user && typeof user === \"object\" && \"role_name\" in user\r\n          ? user.role_name\r\n          : \"\";\r\n\r\n      // First try permission-based access control\r\n      const routePermission = getRoutePermission(pathname);\r\n\r\n      let hasAccess = true;\r\n      let accessMethod = \"default\";\r\n\r\n      // For MVP, prioritize role-based access control\r\n      hasAccess = hasRouteAccess(pathname, roleName);\r\n      accessMethod = \"role\";\r\n\r\n      // Add detailed debug logging\r\n      console.log(`[RoleGuard] Route: ${pathname}`);\r\n      console.log(`[RoleGuard] User role: ${roleName}`);\r\n      console.log(`[RoleGuard] Using role-based access (MVP approach)`);\r\n      console.log(\r\n        `[RoleGuard] Access ${hasAccess ? \"GRANTED\" : \"DENIED\"} by role check`\r\n      );\r\n\r\n      // If we have a permission mapping, check it too (for debugging purposes only)\r\n      if (routePermission) {\r\n        // Map the action to the format used in grants (e.g., \"view\" -> \"read:any\")\r\n        const grantAction = mapActionToGrantAction(routePermission.action);\r\n\r\n        // Check if user has the required permission (but don't use the result)\r\n        const permissionAccess = hasPermission(\r\n          routePermission.resource,\r\n          grantAction,\r\n          routePermission.scope\r\n        );\r\n\r\n        // Log the permission check result for debugging\r\n        console.log(\r\n          `[RoleGuard] Permission check (not used): ${\r\n            routePermission.resource\r\n          }:${routePermission.action}:${routePermission.scope || \"any\"}`\r\n        );\r\n        console.log(`[RoleGuard] Mapped to grant action: ${grantAction}`);\r\n        console.log(\r\n          `[RoleGuard] Permission would be ${\r\n            permissionAccess ? \"GRANTED\" : \"DENIED\"\r\n          } (for future reference)`\r\n        );\r\n      }\r\n\r\n      if (!hasAccess) {\r\n        console.log(\r\n          `[RoleGuard] Access denied for ${pathname}, redirecting to dashboard (method: ${accessMethod})`\r\n        );\r\n\r\n        // Redirect to the appropriate dashboard based on role\r\n        if (roleName === \"accountant\" || roleName === \"finance_manager\") {\r\n          console.log(`[RoleGuard] Redirecting to finance dashboard`);\r\n          router.replace(\"/dashboard\");\r\n          // router.replace(\"/dashboard/finance\");\r\n        } else if (roleName === \"stock_admin\") {\r\n          console.log(`[RoleGuard] Redirecting to stock dashboard`);\r\n          router.replace(\"/dashboard\");\r\n\r\n          // router.replace(\"/dashboard/stock\");\r\n        } else if (\r\n          roleName === \"operations\" ||\r\n          roleName === \"operations_manager\"\r\n        ) {\r\n          console.log(`[RoleGuard] Redirecting to operations dashboard`);\r\n          router.replace(\"/dashboard\");\r\n          // router.replace(\"/dashboard/operations\");\r\n        } else if (roleName === \"float_manager\") {\r\n          console.log(`[RoleGuard] Redirecting to float dashboard`);\r\n          // router.replace(\"/dashboard/float\");\r\n          router.replace(\"/dashboard\");\r\n        } else if (\r\n          roleName === \"pos_operator\" ||\r\n          roleName === \"shop_attendant\"\r\n        ) {\r\n          console.log(`[RoleGuard] Redirecting to POS dashboard`);\r\n          // router.replace(\"/dashboard/pos\");\r\n          router.replace(\"/dashboard\");\r\n        } else if (roleName === \"company_admin\") {\r\n          console.log(`[RoleGuard] Redirecting to company dashboard`);\r\n          // router.replace(\"/dashboard/company\");\r\n          router.replace(\"/dashboard\");\r\n        } else {\r\n          console.log(`[RoleGuard] Redirecting to main dashboard`);\r\n          router.replace(\"/dashboard\");\r\n        }\r\n      }\r\n\r\n      // User data is loaded, we can stop checking and mark as shown\r\n      setLoading(\"role\", false);\r\n      markLoadingShown(\"role\");\r\n    } else if (!isUserLoading && !isPermissionsLoading) {\r\n      // No user data and not loading, stop checking\r\n      setLoading(\"role\", false);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    user,\r\n    isUserLoading,\r\n    isPermissionsLoading,\r\n    pathname,\r\n    router,\r\n    isInitialMount,\r\n    hasShownLoading.role,\r\n    hasPermission,\r\n  ]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in checking state\r\n  useEffect(() => {\r\n    let timeoutId: NodeJS.Timeout | null = null;\r\n\r\n    if (isLoading.role) {\r\n      timeoutId = setTimeout(() => {\r\n        setLoading(\"role\", false);\r\n        markLoadingShown(\"role\");\r\n      }, 1500); // 1.5 second timeout for better UX\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.role]);\r\n\r\n  // Show loading state only when actively checking role access\r\n  if (isLoading.role) {\r\n    return <LoadingScreen message=\"Checking access...\" />;\r\n  }\r\n\r\n  // Don't block rendering, the useEffect will handle redirects\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA,2NAAmD,kCAAkC;AACrF;;;AARA;;;;;;;;AAaA;;;;CAIC,GACD,SAAS,uBAAuB,MAAc;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,cAAc,4BAA4B;QACnD,KAAK;YACH,OAAO,cAAc,8BAA8B;QACrD,KAAK;YACH,OAAO,YAAY,0BAA0B;QAC/C;YACE,OAAO,GAAG,OAAO,IAAI,CAAC;IAC1B;AACF;AAMO,SAAS,UAAU,EAAE,QAAQ,EAAkB;;IACpD,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EAAE,aAAa,EAAE,WAAW,oBAAoB,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;IAEX,qCAAqC;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,kBAAkB;QACpB;8BAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,8BAA8B;YAC9B,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,uBACpB,SAAS,UAAU,CAAC,oBACpB;gBACA,WAAW,QAAQ;gBACnB;YACF;YAEA,4EAA4E;YAC5E,iEAAiE;YACjE,IAAI,kBAAkB,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;gBAC5D,WAAW,QAAQ;gBACnB;YACF,OAAO;gBACL,WAAW,QAAQ;YACrB;YAEA,+DAA+D;YAC/D,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,MAAM;gBACnD,6CAA6C;gBAC7C,MAAM,WACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;gBAEN,4CAA4C;gBAC5C,MAAM,kBAAkB,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAE3C,IAAI,YAAY;gBAChB,IAAI,eAAe;gBAEnB,gDAAgD;gBAChD,YAAY,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBACrC,eAAe;gBAEf,6BAA6B;gBAC7B,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;gBAC5C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU;gBAChD,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;gBAChE,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,YAAY,YAAY,SAAS,cAAc,CAAC;gBAGxE,8EAA8E;gBAC9E,IAAI,iBAAiB;oBACnB,2EAA2E;oBAC3E,MAAM,cAAc,uBAAuB,gBAAgB,MAAM;oBAEjE,uEAAuE;oBACvE,MAAM,mBAAmB,cACvB,gBAAgB,QAAQ,EACxB,aACA,gBAAgB,KAAK;oBAGvB,gDAAgD;oBAChD,QAAQ,GAAG,CACT,CAAC,yCAAyC,EACxC,gBAAgB,QAAQ,CACzB,CAAC,EAAE,gBAAgB,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,IAAI,OAAO;oBAEhE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,aAAa;oBAChE,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAC/B,mBAAmB,YAAY,SAChC,uBAAuB,CAAC;gBAE7B;gBAEA,IAAI,CAAC,WAAW;oBACd,QAAQ,GAAG,CACT,CAAC,8BAA8B,EAAE,SAAS,oCAAoC,EAAE,aAAa,CAAC,CAAC;oBAGjG,sDAAsD;oBACtD,IAAI,aAAa,gBAAgB,aAAa,mBAAmB;wBAC/D,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;wBAC1D,OAAO,OAAO,CAAC;oBACf,wCAAwC;oBAC1C,OAAO,IAAI,aAAa,eAAe;wBACrC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;wBACxD,OAAO,OAAO,CAAC;oBAEf,sCAAsC;oBACxC,OAAO,IACL,aAAa,gBACb,aAAa,sBACb;wBACA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,CAAC;wBAC7D,OAAO,OAAO,CAAC;oBACf,2CAA2C;oBAC7C,OAAO,IAAI,aAAa,iBAAiB;wBACvC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,CAAC;wBACxD,sCAAsC;wBACtC,OAAO,OAAO,CAAC;oBACjB,OAAO,IACL,aAAa,kBACb,aAAa,kBACb;wBACA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC;wBACtD,oCAAoC;wBACpC,OAAO,OAAO,CAAC;oBACjB,OAAO,IAAI,aAAa,iBAAiB;wBACvC,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;wBAC1D,wCAAwC;wBACxC,OAAO,OAAO,CAAC;oBACjB,OAAO;wBACL,QAAQ,GAAG,CAAC,CAAC,yCAAyC,CAAC;wBACvD,OAAO,OAAO,CAAC;oBACjB;gBACF;gBAEA,8DAA8D;gBAC9D,WAAW,QAAQ;gBACnB,iBAAiB;YACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;gBAClD,8CAA8C;gBAC9C,WAAW,QAAQ;YACrB;QACA,uDAAuD;QACzD;8BAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,IAAI;QACpB;KACD;IAED,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,YAAmC;YAEvC,IAAI,UAAU,IAAI,EAAE;gBAClB,YAAY;2CAAW;wBACrB,WAAW,QAAQ;wBACnB,iBAAiB;oBACnB;0CAAG,OAAO,mCAAmC;YAC/C;YAEA;uCAAO;oBACL,IAAI,WAAW,aAAa;gBAC9B;;QACA,uDAAuD;QACzD;8BAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6DAA6D;IAC7D,IAAI,UAAU,IAAI,EAAE;QAClB,qBAAO,6LAAC,yJAAA,CAAA,gBAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,6DAA6D;IAC7D,qBAAO;kBAAG;;AACZ;GA/KgB;;QACmC,kJAAA,CAAA,iBAAc;QACJ,yJAAA,CAAA,iBAAc;QAC1D,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QAE1B,yJAAA,CAAA,aAAU;;;KANE", "debugId": null}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;YACvE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 1328, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { VariantProps, cva } from \"class-variance-authority\";\r\nimport { PanelLeftIcon } from \"lucide-react\";\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = \"16rem\";\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\";\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\";\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\";\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\";\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\";\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  // Use useMemo to prevent recreating the button on every render\r\n  const button = React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, variant, className, props]);\r\n\r\n  // Use useMemo for tooltipContent to prevent recalculation on every render\r\n  const tooltipContent = React.useMemo(() => {\r\n    if (!tooltip) return null;\r\n    return typeof tooltip === \"string\" ? { children: tooltip } : tooltip;\r\n  }, [tooltip]);\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  // Use useMemo for the hidden prop to prevent recalculation on every render\r\n  const isHidden = React.useMemo(() => {\r\n    return state !== \"collapsed\" || isMobile;\r\n  }, [state, isMobile]);\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={isHidden}\r\n        {...tooltipContent}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, className, showOnHover, props]);\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [className, props]);\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean;\r\n  size?: \"sm\" | \"md\";\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, className, props]);\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;gDAC9B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;QACpG;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sDAAE;YACtC,OAAO,WAAW;8DAAc,CAAC,OAAS,CAAC;+DAAQ;8DAAQ,CAAC,OAAS,CAAC;;QACxE;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;qCAAE;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;iDAC/B,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,sIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IAhGS;;QAaU,gIAAA,CAAA,cAAW;;;KAbrB;AAkGT,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,oIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,oIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,oIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6LAAC,oIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IAxBS;;QAKmB;;;MALnB;AA0BT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,6LAAC,wIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,+rBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;;IAChD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,+DAA+D;IAC/D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;6CAAE,kBAC3B,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,aAAW;gBACX,eAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;oBAAE;oBAAS;gBAAK,IAAI;gBAC3D,GAAG,KAAK;;;;;;4CAEV;QAAC;QAAM;QAAM;QAAU;QAAS;QAAW;KAAM;IAEpD,0EAA0E;IAC1E,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qDAAE;YACnC,IAAI,CAAC,SAAS,OAAO;YACrB,OAAO,OAAO,YAAY,WAAW;gBAAE,UAAU;YAAQ,IAAI;QAC/D;oDAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,2EAA2E;IAC3E,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;+CAAE;YAC7B,OAAO,UAAU,eAAe;QAClC;8CAAG;QAAC;QAAO;KAAS;IAEpB,qBACE,6LAAC,sIAAA,CAAA,UAAO;;0BACN,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,sIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ;gBACP,GAAG,cAAc;;;;;;;;;;;;AAI1B;IAtDS;;QAcqB;;;OAdrB;AAwDT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;gBAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;gBAED,GAAG,KAAK;;;;;;oCAEV;QAAC;QAAM;QAAW;QAAa;KAAM;AAC1C;IA/BS;OAAA;AAiCT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;;IAC5B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;oCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;gBAED,GAAG,KAAK;;;;;;mCAEV;QAAC;QAAW;KAAM;AACvB;IArBS;OAAA;AAuBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;8CAAE;YAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;QAClD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IApCS;OAAA;AAsCT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;wCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,aAAW;gBACX,eAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;gBAED,GAAG,KAAK;;;;;;uCAEV;QAAC;QAAM;QAAM;QAAU;QAAW;KAAM;AAC7C;IA/BS;OAAA", "debugId": null}}, {"offset": {"line": 2134, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/navigation-link.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { ReactNode, MouseEvent, useMemo } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface NavigationLinkProps {\r\n  href: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;\r\n  replace?: boolean;\r\n  prefetch?: boolean;\r\n  scroll?: boolean;\r\n  exact?: boolean;\r\n}\r\n\r\n/**\r\n * NavigationLink component that ensures client-side navigation\r\n * This component wraps Next.js Link component and provides a consistent way to navigate\r\n */\r\nexport function NavigationLink({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(className)}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n      aria-current={isActive ? \"page\" : undefined}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n\r\n/**\r\n * NavigationButton component that looks like a button but navigates like a link\r\n */\r\nexport function NavigationButton({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(\r\n        \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAsBO,SAAS,eAAe,EAC7B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;QACzD;2CAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;QACb,gBAAc,WAAW,SAAS;kBAEjC;;;;;;AAGP;GA5CgB;;QAUC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAXd;AAiDT,SAAS,iBAAiB,EAC/B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;QACzD;6CAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0RACA;QAEF,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;kBAEZ;;;;;;AAGP;IA9CgB;;QAUC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;MAXd", "debugId": null}}, {"offset": {"line": 2255, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6LAAC,0KAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6LAAC,0KAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6LAAC,0KAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS", "debugId": null}}, {"offset": {"line": 2312, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-main.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\nimport { ChevronRight, CircleIcon, type LucideIcon } from \"lucide-react\";\r\n\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: {\r\n    title: string;\r\n    url: string;\r\n    icon?: LucideIcon;\r\n    isActive?: boolean;\r\n    items?: {\r\n      title: string;\r\n      url: string;\r\n    }[];\r\n  }[];\r\n}) {\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          // Special case for Dashboard - make it a direct link\r\n          if (item.title === \"Dashboard\") {\r\n            return (\r\n              <SidebarMenuItem key={item.title}>\r\n                <SidebarMenuButton\r\n                  asChild\r\n                  tooltip={item.title}\r\n                  isActive={item.isActive}\r\n                  className=\"nav-main-item\"\r\n                >\r\n                  <NavigationLink href={item.url}>\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                  </NavigationLink>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          }\r\n\r\n          // For all other items, use the collapsible dropdown\r\n          return (\r\n            <Collapsible\r\n              key={item.title}\r\n              asChild\r\n              defaultOpen={item.isActive}\r\n              className=\"group/collapsible mb-2\"\r\n            >\r\n              <SidebarMenuItem>\r\n                <CollapsibleTrigger asChild>\r\n                  <SidebarMenuButton\r\n                    tooltip={item.title}\r\n                    isActive={item.isActive}\r\n                    className=\"nav-main-item\"\r\n                  >\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                    <ChevronRight className=\"ml-auto h-2 w-2 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 nav-main-item-chevron\" />\r\n                  </SidebarMenuButton>\r\n                </CollapsibleTrigger>\r\n                <CollapsibleContent>\r\n                  <SidebarMenuSub className=\"nav-main-sub mt-2\">\r\n                    {item.items?.map((subItem) => (\r\n                      <SidebarMenuSubItem key={subItem.title}>\r\n                        <SidebarMenuSubButton asChild>\r\n                          <NavigationLink href={subItem.url} exact={true}>\r\n                            {/* No circle icon for child items */}\r\n                            <span className=\"text-xs\">{subItem.title}</span>\r\n                          </NavigationLink>\r\n                        </SidebarMenuSubButton>\r\n                      </SidebarMenuSubItem>\r\n                    ))}\r\n                  </SidebarMenuSub>\r\n                </CollapsibleContent>\r\n              </SidebarMenuItem>\r\n            </Collapsible>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAKA;AAVA;;;;;;AAoBO,SAAS,QAAQ,EACtB,KAAK,EAYN;IACC,qBACE,6LAAC,sIAAA,CAAA,eAAY;kBACX,cAAA,6LAAC,sIAAA,CAAA,cAAW;sBACT,MAAM,GAAG,CAAC,CAAC;gBACV,qDAAqD;gBACrD,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kCACd,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,SAAS,KAAK,KAAK;4BACnB,UAAU,KAAK,QAAQ;4BACvB,WAAU;sCAEV,cAAA,6LAAC,iJAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,GAAG;;kDAC5B,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CAClC,KAAK,QAAQ,kBACZ,6LAAC,6MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAG1B,6LAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAfG,KAAK,KAAK;;;;;gBAqBpC;gBAEA,oDAAoD;gBACpD,qBACE,6LAAC,0IAAA,CAAA,cAAW;oBAEV,OAAO;oBACP,aAAa,KAAK,QAAQ;oBAC1B,WAAU;8BAEV,cAAA,6LAAC,sIAAA,CAAA,kBAAe;;0CACd,6LAAC,0IAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;oCAChB,SAAS,KAAK,KAAK;oCACnB,UAAU,KAAK,QAAQ;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDAClC,KAAK,QAAQ,kBACZ,6LAAC,6MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAG1B,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;sDAEb,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG5B,6LAAC,0IAAA,CAAA,qBAAkB;0CACjB,cAAA,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,6LAAC,sIAAA,CAAA,qBAAkB;sDACjB,cAAA,6LAAC,sIAAA,CAAA,uBAAoB;gDAAC,OAAO;0DAC3B,cAAA,6LAAC,iJAAA,CAAA,iBAAc;oDAAC,MAAM,QAAQ,GAAG;oDAAE,OAAO;8DAExC,cAAA,6LAAC;wDAAK,WAAU;kEAAW,QAAQ,KAAK;;;;;;;;;;;;;;;;2CAJrB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;mBA3BzC,KAAK,KAAK;;;;;YAyCrB;;;;;;;;;;;AAIR;KA5FgB", "debugId": null}}, {"offset": {"line": 2530, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 2592, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 2888, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-user.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Chev<PERSON>UpDown, LogOut } from \"lucide-react\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavUser({\r\n  user,\r\n}: {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n  };\r\n}) {\r\n  const { isMobile } = useSidebar();\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground nav-user\"\r\n            >\r\n              <Avatar className=\"h-6 w-6 rounded-lg nav-user-avatar\">\r\n                <AvatarImage src={user.avatar} alt={user.name} />\r\n                <AvatarFallback className=\"rounded-lg text-xs\">\r\n                  {user.name.charAt(0)}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left leading-tight nav-user-details\">\r\n                <span className=\"truncate text-xs font-medium\">\r\n                  {user.name}\r\n                </span>\r\n                <span className=\"truncate text-[10px]\">{user.email}</span>\r\n              </div>\r\n              <ChevronsUpDown className=\"ml-auto h-3 w-3 nav-user-chevron\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar} alt={user.name} />\r\n                  <AvatarFallback className=\"rounded-lg\">CN</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name}</span>\r\n                  <span className=\"truncate text-xs\">{user.email}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem>\r\n                <BadgeCheck className=\"mr-2 h-3.5 w-3.5\" />\r\n                <span className=\"text-xs\">Account</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem>\r\n              <LogOut className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"text-xs\">Log out</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AASA;;;AAdA;;;;;AAqBO,SAAS,QAAQ,EACtB,IAAI,EAOL;;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IAE9B,qBACE,6LAAC,sIAAA,CAAA,cAAW;kBACV,cAAA,6LAAC,sIAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC,+IAAA,CAAA,eAAY;;kCACX,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,6LAAC,qIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,6LAAC,qIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;8CAGtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAAwB,KAAK,KAAK;;;;;;;;;;;;8CAEpD,6LAAC,iOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,6LAAC,+IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,6LAAC,+IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,6LAAC;oDAAK,WAAU;8DAAoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIpD,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC,+IAAA,CAAA,oBAAiB;0CAChB,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;;sDACf,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC,+IAAA,CAAA,mBAAgB;;kDACf,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GAtEgB;;QASO,sIAAA,CAAA,aAAU;;;KATjB", "debugId": null}}, {"offset": {"line": 3160, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport \"@/components/ui/sidebar-collapsed.css\";\r\nimport \"@/components/ui/sidebar-compact.css\";\r\nimport {\r\n  Banknote,\r\n  BarChart3 as BarChart3Icon,\r\n  Building as BuildingIcon,\r\n  CreditCard,\r\n  FileText,\r\n  GalleryVerticalEnd,\r\n  Home as HomeIcon,\r\n  Landmark,\r\n  Package,\r\n  Settings,\r\n  ShoppingCart,\r\n  Smartphone,\r\n  UserCheck,\r\n} from \"lucide-react\";\r\n\r\nimport { NavMain } from \"@/components/nav-main\";\r\nimport { NavUser } from \"@/components/nav-user\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n// Import usePermissions for future use\r\n// import { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\n// import { getRoutePermission } from \"@/lib/route-permissions\";\r\n// import { navigationPermissions } from \"@/lib/navigation-permissions\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\ntype NavItem = {\r\n  title: string;\r\n  url: string;\r\n  icon?: any;\r\n  isActive?: boolean;\r\n  items?: { title: string; url: string }[];\r\n};\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * This function is commented out for MVP, will be used in the future\r\n * when the permission system is fully implemented\r\n *\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\n// function mapActionToGrantAction(action: string): string {\r\n//   switch (action) {\r\n//     case \"view\":\r\n//       return \"read:any\";\r\n//     case \"create\":\r\n//       return \"create:any\";\r\n//     case \"update\":\r\n//       return \"update:any\";\r\n//     case \"delete\":\r\n//       return \"delete:any\";\r\n//     case \"manage\":\r\n//       return \"update:any\"; // Manage maps to update:any\r\n//     case \"transfer\":\r\n//       return \"update:any\"; // Transfer maps to update:any\r\n//     case \"report\":\r\n//       return \"read:any\"; // Report maps to read:any\r\n//     default:\r\n//       return `${action}:any`;\r\n//   }\r\n// }\r\n\r\nexport function AppSidebar() {\r\n  const { data: user } = useCurrentUser();\r\n  const pathname = usePathname();\r\n  // Permissions will be used in the future\r\n  // const { hasPermission, permissions } = usePermissions();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  // Define all navigation items\r\n  const getAllNavigationItems = (): NavItem[] => [\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/dashboard\",\r\n      icon: HomeIcon,\r\n      isActive: pathname === \"/dashboard\" || pathname === \"/\",\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      url: \"/reports/sales-summary\",\r\n      icon: BarChart3Icon,\r\n      isActive:\r\n        pathname.startsWith(\"/reports\") ||\r\n        pathname.startsWith(\"/expense-analytics\"),\r\n      items: [\r\n        {\r\n          title: \"Sales Summary\",\r\n          url: \"/reports/sales-summary\",\r\n        },\r\n        {\r\n          title: \"Sales by Item\",\r\n          url: \"/reports/sales-by-item\",\r\n        },\r\n        {\r\n          title: \"Sales by Category\",\r\n          url: \"/reports/sales-by-category\",\r\n        },\r\n        {\r\n          title: \"Current Stock Levels\",\r\n          url: \"/reports/current-stock-levels\",\r\n        },\r\n        {\r\n          title: \"Stock History\",\r\n          url: \"/reports/stock-history\",\r\n        },\r\n        {\r\n          title: \"Banking Transactions\",\r\n          url: \"/reports/banking-transactions\",\r\n        },\r\n        {\r\n          title: \"Tax Report\",\r\n          url: \"/reports/tax-report\",\r\n        },\r\n        {\r\n          title: \"Banking Summary\",\r\n          url: \"/banking?tab=summary\",\r\n        },\r\n        {\r\n          title: \"M-Pesa Transactions\",\r\n          url: \"/reports/mpesa-transactions\",\r\n        },\r\n        {\r\n          title: \"Running Balances\",\r\n          url: \"/reports/running-balances\",\r\n        },\r\n        {\r\n          title: \"Cash Status\",\r\n          url: \"/reports/cash-status\",\r\n        },\r\n        {\r\n          title: \"Cash Float\",\r\n          url: \"/reports/cash-float\",\r\n        },\r\n        {\r\n          title: \"DSA Sales\",\r\n          url: \"/reports/dsa-sales\",\r\n        },\r\n        {\r\n          title: \"Receipts\",\r\n          url: \"/reports/receipts\",\r\n        },\r\n        {\r\n          title: \"Shifts\",\r\n          url: \"/reports/shifts\",\r\n        },\r\n        {\r\n          title: \"Phone Repairs\",\r\n          url: \"/reports/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"Stock Valuation\",\r\n          url: \"/inventory/valuation\",\r\n        },\r\n        {\r\n          title: \"Stock Aging\",\r\n          url: \"/reports/stock-aging\",\r\n        },\r\n        {\r\n          title: \"Stock Movement\",\r\n          url: \"/reports/stock-movement\",\r\n        },\r\n        {\r\n          title: \"Expense Analytics\",\r\n          url: \"/expense-analytics\",\r\n        },\r\n        {\r\n          title: \"Customers\",\r\n          url: \"/customers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Administration\",\r\n      url: \"/users\",\r\n      icon: BuildingIcon,\r\n      isActive:\r\n        pathname.startsWith(\"/users\") ||\r\n        pathname.startsWith(\"/roles\") ||\r\n        pathname.startsWith(\"/rbac\") ||\r\n        pathname.startsWith(\"/branches\") ||\r\n        pathname.startsWith(\"/locations\") ||\r\n        pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        // Tenants menu item removed\r\n        {\r\n          title: \"Users\",\r\n          url: \"/users\",\r\n        },\r\n        {\r\n          title: \"Roles\",\r\n          url: \"/roles\",\r\n        },\r\n        {\r\n          title: \"RBAC\",\r\n          url: \"/rbac\",\r\n        },\r\n        {\r\n          title: \"Branches\",\r\n          url: \"/branches\",\r\n        },\r\n        {\r\n          title: \"Locations\",\r\n          url: \"/locations\",\r\n        },\r\n        {\r\n          title: \"Employees\",\r\n          url: \"/employees\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Stock & Inventory\",\r\n      url: \"/products\",\r\n      icon: Package,\r\n      isActive:\r\n        pathname.startsWith(\"/products\") ||\r\n        pathname.startsWith(\"/categories\") ||\r\n        pathname.startsWith(\"/brands\") ||\r\n        pathname.startsWith(\"/inventory\") ||\r\n        pathname.includes(\"/inventory/stock-cards\") ||\r\n        pathname.includes(\"/inventory/purchases\"),\r\n      items: [\r\n        {\r\n          title: \"Products\",\r\n          url: \"/products\",\r\n        },\r\n        {\r\n          title: \"Categories\",\r\n          url: \"/categories\",\r\n        },\r\n        {\r\n          title: \"Brands\",\r\n          url: \"/brands\",\r\n        },\r\n        {\r\n          title: \"Stock levels\",\r\n          url: \"/inventory\",\r\n        },\r\n        // Commented out Stock Locations as requested\r\n        // {\r\n        //   title: \"Stock Locations\",\r\n        //   url: \"/inventory/locations\",\r\n        // },\r\n        {\r\n          title: \"Purchases\",\r\n          url: \"/inventory/purchases\",\r\n        },\r\n        {\r\n          title: \"Create Purchase\",\r\n          url: \"/inventory/purchases/new\",\r\n        },\r\n        {\r\n          title: \"Stock Requests\",\r\n          url: \"/inventory/stock-requests\",\r\n        },\r\n        {\r\n          title: \"Stock Transfers\",\r\n          url: \"/inventory/transfers\",\r\n        },\r\n        {\r\n          title: \"Make Stock Transfer\",\r\n          url: \"/inventory/bulk-transfer\",\r\n        },\r\n        {\r\n          title: \"Stock Transfer History\",\r\n          url: \"/inventory/bulk-transfers\",\r\n        },\r\n        // {\r\n        //   title: \"Stock Cards\",\r\n        //   url: \"/inventory/stock-cards\",\r\n        // },\r\n        {\r\n          title: \"Inventory Reports\",\r\n          url: \"/inventory/reports\",\r\n        },\r\n        {\r\n          title: \"Excel Import/Export\",\r\n          url: \"/inventory/excel\",\r\n        },\r\n        {\r\n          title: \"Price List\",\r\n          url: \"/inventory/price-list\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Float Management\",\r\n      url: \"/float\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/float\"),\r\n      items: [\r\n        {\r\n          title: \"Float Dashboard\",\r\n          url: \"/float\",\r\n        },\r\n        {\r\n          title: \"Float Movements\",\r\n          url: \"/float/movements\",\r\n        },\r\n        {\r\n          title: \"Float Reconciliations\",\r\n          url: \"/float/reconciliations\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Banking Management\",\r\n      url: \"/banking\",\r\n      icon: Landmark,\r\n      isActive: pathname.startsWith(\"/banking\"),\r\n      items: [\r\n        {\r\n          title: \"Banking Records\",\r\n          url: \"/banking\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Expenses Management\",\r\n      url: \"/expenses\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/expenses\"),\r\n      items: [\r\n        {\r\n          title: \"Expenses\",\r\n          url: \"/expenses\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Invoice Management\",\r\n      url: \"/invoices\",\r\n      icon: FileText,\r\n      isActive:\r\n        pathname.startsWith(\"/invoices\") ||\r\n        pathname.startsWith(\"/credit-notes\"),\r\n      items: [\r\n        {\r\n          title: \"All Invoices\",\r\n          url: \"/invoices\",\r\n        },\r\n        {\r\n          title: \"Credit Notes\",\r\n          url: \"/credit-notes\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"DSA Management\",\r\n      url: \"/dsa\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/dsa\"),\r\n      items: [\r\n        {\r\n          title: \"DSA Dashboard\",\r\n          url: \"/dsa\",\r\n        },\r\n        {\r\n          title: \"DSA Agents\",\r\n          url: \"/dsa/customers\",\r\n        },\r\n        {\r\n          title: \"Stock Assignments\",\r\n          url: \"/dsa/assignments\",\r\n        },\r\n        // Commented out DSA Reconciliations as requested\r\n        // {\r\n        //   title: \"DSA Reconciliations\",\r\n        //   url: \"/dsa/reconciliations\",\r\n        // },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Procurement\",\r\n      url: \"/procurement\",\r\n      icon: ShoppingCart,\r\n      isActive:\r\n        pathname.startsWith(\"/procurement\") ||\r\n        pathname.startsWith(\"/suppliers\"),\r\n      items: [\r\n        {\r\n          title: \"Procurement Dashboard\",\r\n          url: \"/procurement\",\r\n        },\r\n        {\r\n          title: \"Procurement Requests\",\r\n          url: \"/procurement/requests\",\r\n        },\r\n        {\r\n          title: \"Create Request\",\r\n          url: \"/procurement/requests/new\",\r\n        },\r\n        {\r\n          title: \"Procurement Receipts\",\r\n          url: \"/procurement/receipts\",\r\n        },\r\n        {\r\n          title: \"Suppliers\",\r\n          url: \"/suppliers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Phone Repairs\",\r\n      url: \"/phone-repairs\",\r\n      icon: Smartphone,\r\n      isActive:\r\n        pathname === \"/phone-repairs\" || pathname.startsWith(\"/phone-repairs/\")\r\n          ? true\r\n          : false,\r\n      items: [\r\n        {\r\n          title: \"All Repairs\",\r\n          url: \"/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"New Repair\",\r\n          url: \"/phone-repairs/new\",\r\n        },\r\n      ],\r\n    },\r\n    // Settings section completely hidden for production - not implemented\r\n    // {\r\n    //   title: \"Settings\",\r\n    //   url: \"/settings\",\r\n    //   icon: Settings,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/settings\") || pathname.startsWith(\"/profile\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"System Settings\",\r\n    //       url: \"/settings/system\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Company Settings\",\r\n    //       url: \"/settings/company\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Payment Methods\",\r\n    //       url: \"/settings/payment-methods\",\r\n    //     },\r\n    //     {\r\n    //       title: \"System Health\",\r\n    //       url: \"/settings/health\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Profile\",\r\n    //       url: \"/profile\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    // Permissions Demo section removed\r\n  ];\r\n\r\n  // Filter navigation items based on role (MVP approach)\r\n  const getFilteredNavigationItemsByRole = (): NavItem[] => {\r\n    const allItems = getAllNavigationItems();\r\n\r\n    // Create a custom Employees section for branch managers\r\n    const employeesSection: NavItem = {\r\n      title: \"Employees\",\r\n      url: \"/employees\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        {\r\n          title: \"Manage Employees\",\r\n          url: \"/employees\",\r\n        },\r\n        {\r\n          title: \"Add Employee\",\r\n          url: \"/employees/create\",\r\n        },\r\n      ],\r\n    };\r\n\r\n    // For MVP, use role-based filtering as the default approach\r\n    console.log(\r\n      `[Navigation] ${userRoleName} role detected - using role-based filtering (MVP approach)`\r\n    );\r\n\r\n    // Filter navigation items based on user role\r\n    const filteredItems = allItems.filter((item) => {\r\n      // For super_admin and company_admin, show all items\r\n      if (userRoleName === \"super_admin\" || userRoleName === \"company_admin\") {\r\n        console.log(`[Navigation] Admin role detected - showing all items`);\r\n        return true;\r\n      }\r\n\r\n      // For branch_admin, show most items\r\n      if (userRoleName === \"branch_admin\") {\r\n        // Hide Tenants section\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n        return true;\r\n      }\r\n\r\n      // For accountant, show finance-related items\r\n      if (userRoleName === \"accountant\") {\r\n        // Allow Dashboard\r\n        // Hide Tenants section\r\n        if (item.title === \"Dashboard\") {\r\n          return true;\r\n        }\r\n\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Administration routes for accountant\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow Reports, Banking, Expenses, Float\r\n        if (\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide DSA Management\r\n        if (item.title === \"DSA Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Phone Repairs\r\n        if (item.title === \"Phone Repairs\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow POS Management for viewing sales data\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide Settings for accountant\r\n        if (item.title === \"Settings\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For auditor, show reporting and read-only sections\r\n      if (userRoleName === \"auditor\") {\r\n        // Allow Reports, Banking, Expenses, Float (read-only)\r\n        if (\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"POS Management\" ||\r\n          item.title === \"Products & Inventory\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For finance_manager, show finance-related sections\r\n      if (userRoleName === \"finance_manager\") {\r\n        // Allow Reports, Banking, Expenses, Float\r\n        if (\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Expenses Management\" ||\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"POS Management\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For float_manager, show float-related sections\r\n      if (userRoleName === \"float_manager\") {\r\n        if (item.title === \"Dashboard\") {\r\n          return true;\r\n        }\r\n\r\n        // Allow Float Management, Banking, and Reports\r\n        if (\r\n          item.title === \"Float Management\" ||\r\n          item.title === \"Banking Management\" ||\r\n          item.title === \"Reports\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Allow POS Management but only for Cash Balance access\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For operations and operations_manager, show operations-related sections\r\n      if (\r\n        userRoleName === \"operations\" ||\r\n        userRoleName === \"operations_manager\"\r\n      ) {\r\n        // Hide Administration routes\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Tenants section\r\n        if (item.title === \"Tenants\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow everything else\r\n        return true;\r\n      }\r\n\r\n      // For stock_admin, show inventory-related sections\r\n      if (userRoleName === \"stock_admin\") {\r\n        // Allow Products & Inventory\r\n        if (\r\n          item.title === \"Products & Inventory\" ||\r\n          item.title === \"Reports\" ||\r\n          item.title === \"Dashboard\" ||\r\n          item.title === \"Procurement\"\r\n        ) {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // Branch Manager role removed as they don't login via the web\r\n\r\n      // For pos_operator and shop_attendant, hide admin sections\r\n      if (\r\n        userRoleName === \"pos_operator\" ||\r\n        userRoleName === \"shop_attendant\"\r\n      ) {\r\n        // Hide Administration routes\r\n        if (item.title === \"Administration\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Float Management\r\n        if (item.title === \"Float Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Banking Management\r\n        if (item.title === \"Banking Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide Expenses Management\r\n        if (item.title === \"Expenses Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Hide DSA Management\r\n        if (item.title === \"DSA Management\") {\r\n          return false;\r\n        }\r\n\r\n        // Allow POS Management\r\n        if (item.title === \"POS Management\") {\r\n          return true;\r\n        }\r\n\r\n        // Allow Products & Inventory (read-only)\r\n        if (item.title === \"Products & Inventory\") {\r\n          return true;\r\n        }\r\n\r\n        // Hide other sections\r\n        return false;\r\n      }\r\n\r\n      // For other roles, restrict access to only essential items\r\n      console.log(\r\n        `[Navigation] Unknown role detected: ${userRoleName} - restricting access`\r\n      );\r\n\r\n      // Allow Dashboard only\r\n      if (item.title === \"Dashboard\") {\r\n        return true;\r\n      }\r\n\r\n      // Hide Settings and everything else for unknown roles\r\n      return false;\r\n    });\r\n\r\n    // Filter subitems based on role\r\n    const itemsWithFilteredSubitems = filteredItems.map((item) => {\r\n      // If the item has subitems, filter them based on role\r\n      if (item.items && item.items.length > 0) {\r\n        console.log(\r\n          `[Navigation] Checking subitems for \"${item.title}\" section`\r\n        );\r\n\r\n        const filteredSubItems = item.items.filter((subItem) => {\r\n          // For Administration, filter RBAC items for non-super_admin users\r\n          if (item.title === \"Administration\") {\r\n            // Only super_admin can see RBAC items\r\n            if (subItem.title === \"RBAC\") {\r\n              return userRoleName === \"super_admin\";\r\n            }\r\n            // All other admin roles can see other items\r\n            return true;\r\n          }\r\n\r\n          // For Settings, only show Profile for most roles\r\n          if (item.title === \"Settings\") {\r\n            // Admin roles can see all settings\r\n            if (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\"\r\n            ) {\r\n              return true;\r\n            }\r\n\r\n            // Accountant can see Profile and Payment Methods\r\n            if (userRoleName === \"accountant\") {\r\n              return (\r\n                subItem.title === \"Profile\" ||\r\n                subItem.title === \"Payment Methods\"\r\n              );\r\n            }\r\n\r\n            // Other roles can only see Profile\r\n            return subItem.title === \"Profile\";\r\n          }\r\n\r\n          // For POS Management, restrict access based on role\r\n          if (item.title === \"POS Management\") {\r\n            // For float_manager, only show Cash Balance\r\n            if (userRoleName === \"float_manager\") {\r\n              return subItem.title === \"Cash Balance\";\r\n            }\r\n\r\n            // For other roles, restrict Cash Balance to admin roles\r\n            if (subItem.title === \"Cash Balance\") {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Products & Inventory, restrict Categories and Brands for non-admin roles\r\n          if (\r\n            item.title === \"Products & Inventory\" &&\r\n            (subItem.title.includes(\"Categories\") ||\r\n              subItem.title.includes(\"Brands\"))\r\n          ) {\r\n            return (\r\n              userRoleName === \"super_admin\" ||\r\n              userRoleName === \"company_admin\" ||\r\n              userRoleName === \"branch_admin\" ||\r\n              userRoleName === \"stock_admin\"\r\n            );\r\n          }\r\n\r\n          // For Products & Inventory, restrict inventory management for shop_attendant and pos_operator\r\n          if (\r\n            item.title === \"Products & Inventory\" &&\r\n            (subItem.title.includes(\"Transfer\") ||\r\n              subItem.title.includes(\"Stock Cards\") ||\r\n              subItem.title.includes(\"Inventory Reports\") ||\r\n              subItem.title.includes(\"Excel\"))\r\n          ) {\r\n            return (\r\n              userRoleName !== \"pos_operator\" &&\r\n              userRoleName !== \"shop_attendant\"\r\n            );\r\n          }\r\n\r\n          // For Reports, restrict certain reports based on role\r\n          if (item.title === \"Reports\") {\r\n            // Finance-related reports\r\n            if (\r\n              subItem.title.includes(\"Banking\") ||\r\n              subItem.title.includes(\"Cash\") ||\r\n              subItem.title.includes(\"Float\") ||\r\n              subItem.title.includes(\"Expense\")\r\n            ) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\" ||\r\n                userRoleName === \"auditor\"\r\n              );\r\n            }\r\n\r\n            // Inventory-related reports\r\n            if (\r\n              subItem.title.includes(\"Stock\") ||\r\n              subItem.title.includes(\"Inventory\")\r\n            ) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"stock_admin\" ||\r\n                userRoleName === \"operations_manager\" ||\r\n                userRoleName === \"operations\" ||\r\n                userRoleName === \"auditor\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Procurement, restrict certain operations based on role\r\n          if (item.title === \"Procurement\") {\r\n            // Creating and approving procurement requests\r\n            if (subItem.title.includes(\"Create\")) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"branch_admin\" ||\r\n                userRoleName === \"stock_admin\" ||\r\n                userRoleName === \"operations_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // For Float Management, restrict certain operations based on role\r\n          if (item.title === \"Float Management\") {\r\n            // Float reconciliations\r\n            if (subItem.title.includes(\"Reconciliation\")) {\r\n              return (\r\n                userRoleName === \"super_admin\" ||\r\n                userRoleName === \"company_admin\" ||\r\n                userRoleName === \"accountant\" ||\r\n                userRoleName === \"finance_manager\" ||\r\n                userRoleName === \"float_manager\"\r\n              );\r\n            }\r\n          }\r\n\r\n          // Allow all other subitems by default\r\n          return true;\r\n        });\r\n\r\n        // Update the item's subitems with the filtered list\r\n        item.items = filteredSubItems;\r\n      }\r\n\r\n      return item;\r\n    });\r\n\r\n    // Only return items that have at least one subitem (if they had subitems to begin with)\r\n    const result = itemsWithFilteredSubitems.filter(\r\n      (item) => !item.items || item.items.length > 0\r\n    );\r\n\r\n    // Add the Employees section at the beginning of the filtered items for branch managers\r\n    if (userRoleName === \"branch_manager\") {\r\n      return [employeesSection, ...result];\r\n    }\r\n\r\n    return result;\r\n  };\r\n\r\n  // Filter navigation items based on permissions (for future use)\r\n  // This function is commented out for MVP, will be implemented in the future\r\n  // when the permission system is fully implemented\r\n\r\n  // Sample data for the sidebar\r\n  const data = {\r\n    user: {\r\n      name: user?.name || \"User\",\r\n      email: user?.email || \"<EMAIL>\",\r\n      avatar: \"/placeholder-avatar.jpg\",\r\n    },\r\n    teams: [\r\n      {\r\n        name: teamData.name,\r\n        logo: GalleryVerticalEnd,\r\n        plan: teamData.plan,\r\n      },\r\n    ],\r\n    // For MVP, use role-based filtering\r\n    navMain: getFilteredNavigationItemsByRole(),\r\n    // For future: navMain: getFilteredNavigationItems(),\r\n  };\r\n\r\n  const { state, isMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\" && !isMobile;\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"h-full flex flex-col sidebar-compact\",\r\n        isCollapsed && \"sidebar-collapsed\"\r\n      )}\r\n    >\r\n      <div className=\"flex-1 overflow-y-auto py-2\">\r\n        <div className={cn(\"px-3\", isCollapsed && \"px-2\")}>\r\n          <NavMain items={data.navMain} />\r\n        </div>\r\n      </div>\r\n      <div className={cn(\"p-3 border-t\", isCollapsed && \"p-2\")}>\r\n        <NavUser user={data.user} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,0EAA0E;AAC1E,gEAAgE;AAChE,wEAAwE;AACxE;AACA;;;AA7BA;;;;;;;;;;AAoEO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,yCAAyC;IACzC,2DAA2D;IAE3D,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,8BAA8B;IAC9B,MAAM,wBAAwB,IAAiB;YAC7C;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,sMAAA,CAAA,OAAQ;gBACd,UAAU,aAAa,gBAAgB,aAAa;YACtD;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,qNAAA,CAAA,YAAa;gBACnB,UACE,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAY;gBAClB,UACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL,4BAA4B;oBAC5B;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,2MAAA,CAAA,UAAO;gBACb,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,QAAQ,CAAC,6BAClB,SAAS,QAAQ,CAAC;gBACpB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,6CAA6C;oBAC7C,IAAI;oBACJ,8BAA8B;oBAC9B,iCAAiC;oBACjC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,IAAI;oBACJ,0BAA0B;oBAC1B,mCAAmC;oBACnC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,iNAAA,CAAA,WAAQ;gBACd,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,mNAAA,CAAA,YAAS;gBACf,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBAMD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,yNAAA,CAAA,eAAY;gBAClB,UACE,SAAS,UAAU,CAAC,mBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,iNAAA,CAAA,aAAU;gBAChB,UACE,aAAa,oBAAoB,SAAS,UAAU,CAAC,qBACjD,OACA;gBACN,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;SAgCD;IAED,uDAAuD;IACvD,MAAM,mCAAmC;QACvC,MAAM,WAAW;QAEjB,wDAAwD;QACxD,MAAM,mBAA4B;YAChC,OAAO;YACP,KAAK;YACL,MAAM,mNAAA,CAAA,YAAS;YACf,UAAU,SAAS,UAAU,CAAC;YAC9B,OAAO;gBACL;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;QAEA,4DAA4D;QAC5D,QAAQ,GAAG,CACT,CAAC,aAAa,EAAE,aAAa,0DAA0D,CAAC;QAG1F,6CAA6C;QAC7C,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC;YACrC,oDAAoD;YACpD,IAAI,iBAAiB,iBAAiB,iBAAiB,iBAAiB;gBACtE,QAAQ,GAAG,CAAC,CAAC,oDAAoD,CAAC;gBAClE,OAAO;YACT;YAEA,oCAAoC;YACpC,IAAI,iBAAiB,gBAAgB;gBACnC,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBACA,OAAO;YACT;YAEA,6CAA6C;YAC7C,IAAI,iBAAiB,cAAc;gBACjC,kBAAkB;gBAClB,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,OAAO;gBACT;gBAEA,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBAEA,4CAA4C;gBAC5C,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,0CAA0C;gBAC1C,IACE,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,oBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,qBAAqB;gBACrB,IAAI,KAAK,KAAK,KAAK,iBAAiB;oBAClC,OAAO;gBACT;gBAEA,8CAA8C;gBAC9C,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,+BAA+B;gBAC/B,IAAI,KAAK,KAAK,KAAK,YAAY;oBAC7B,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,qDAAqD;YACrD,IAAI,iBAAiB,WAAW;gBAC9B,sDAAsD;gBACtD,IACE,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,oBACf,KAAK,KAAK,KAAK,wBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,qDAAqD;YACrD,IAAI,iBAAiB,mBAAmB;gBACtC,0CAA0C;gBAC1C,IACE,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,yBACf,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,kBACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,iDAAiD;YACjD,IAAI,iBAAiB,iBAAiB;gBACpC,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,OAAO;gBACT;gBAEA,+CAA+C;gBAC/C,IACE,KAAK,KAAK,KAAK,sBACf,KAAK,KAAK,KAAK,wBACf,KAAK,KAAK,KAAK,WACf;oBACA,OAAO;gBACT;gBAEA,wDAAwD;gBACxD,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,0EAA0E;YAC1E,IACE,iBAAiB,gBACjB,iBAAiB,sBACjB;gBACA,6BAA6B;gBAC7B,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,OAAO;YACT;YAEA,mDAAmD;YACnD,IAAI,iBAAiB,eAAe;gBAClC,6BAA6B;gBAC7B,IACE,KAAK,KAAK,KAAK,0BACf,KAAK,KAAK,KAAK,aACf,KAAK,KAAK,KAAK,eACf,KAAK,KAAK,KAAK,eACf;oBACA,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,8DAA8D;YAE9D,2DAA2D;YAC3D,IACE,iBAAiB,kBACjB,iBAAiB,kBACjB;gBACA,6BAA6B;gBAC7B,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,IAAI,KAAK,KAAK,KAAK,oBAAoB;oBACrC,OAAO;gBACT;gBAEA,0BAA0B;gBAC1B,IAAI,KAAK,KAAK,KAAK,sBAAsB;oBACvC,OAAO;gBACT;gBAEA,2BAA2B;gBAC3B,IAAI,KAAK,KAAK,KAAK,uBAAuB;oBACxC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,uBAAuB;gBACvB,IAAI,KAAK,KAAK,KAAK,kBAAkB;oBACnC,OAAO;gBACT;gBAEA,yCAAyC;gBACzC,IAAI,KAAK,KAAK,KAAK,wBAAwB;oBACzC,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO;YACT;YAEA,2DAA2D;YAC3D,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,aAAa,qBAAqB,CAAC;YAG5E,uBAAuB;YACvB,IAAI,KAAK,KAAK,KAAK,aAAa;gBAC9B,OAAO;YACT;YAEA,sDAAsD;YACtD,OAAO;QACT;QAEA,gCAAgC;QAChC,MAAM,4BAA4B,cAAc,GAAG,CAAC,CAAC;YACnD,sDAAsD;YACtD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACvC,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;gBAG9D,MAAM,mBAAmB,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,kEAAkE;oBAClE,IAAI,KAAK,KAAK,KAAK,kBAAkB;wBACnC,sCAAsC;wBACtC,IAAI,QAAQ,KAAK,KAAK,QAAQ;4BAC5B,OAAO,iBAAiB;wBAC1B;wBACA,4CAA4C;wBAC5C,OAAO;oBACT;oBAEA,iDAAiD;oBACjD,IAAI,KAAK,KAAK,KAAK,YAAY;wBAC7B,mCAAmC;wBACnC,IACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,gBACjB;4BACA,OAAO;wBACT;wBAEA,iDAAiD;wBACjD,IAAI,iBAAiB,cAAc;4BACjC,OACE,QAAQ,KAAK,KAAK,aAClB,QAAQ,KAAK,KAAK;wBAEtB;wBAEA,mCAAmC;wBACnC,OAAO,QAAQ,KAAK,KAAK;oBAC3B;oBAEA,oDAAoD;oBACpD,IAAI,KAAK,KAAK,KAAK,kBAAkB;wBACnC,4CAA4C;wBAC5C,IAAI,iBAAiB,iBAAiB;4BACpC,OAAO,QAAQ,KAAK,KAAK;wBAC3B;wBAEA,wDAAwD;wBACxD,IAAI,QAAQ,KAAK,KAAK,gBAAgB;4BACpC,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,gBACjB,iBAAiB;wBAErB;oBACF;oBAEA,+EAA+E;oBAC/E,IACE,KAAK,KAAK,KAAK,0BACf,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,iBACtB,QAAQ,KAAK,CAAC,QAAQ,CAAC,SAAS,GAClC;wBACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB;oBAErB;oBAEA,8FAA8F;oBAC9F,IACE,KAAK,KAAK,KAAK,0BACf,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,eACtB,QAAQ,KAAK,CAAC,QAAQ,CAAC,kBACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,wBACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,QAAQ,GACjC;wBACA,OACE,iBAAiB,kBACjB,iBAAiB;oBAErB;oBAEA,sDAAsD;oBACtD,IAAI,KAAK,KAAK,KAAK,WAAW;wBAC5B,0BAA0B;wBAC1B,IACE,QAAQ,KAAK,CAAC,QAAQ,CAAC,cACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,WACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB;4BACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;wBAErB;wBAEA,4BAA4B;wBAC5B,IACE,QAAQ,KAAK,CAAC,QAAQ,CAAC,YACvB,QAAQ,KAAK,CAAC,QAAQ,CAAC,cACvB;4BACA,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,iBACjB,iBAAiB,wBACjB,iBAAiB,gBACjB,iBAAiB;wBAErB;oBACF;oBAEA,6DAA6D;oBAC7D,IAAI,KAAK,KAAK,KAAK,eAAe;wBAChC,8CAA8C;wBAC9C,IAAI,QAAQ,KAAK,CAAC,QAAQ,CAAC,WAAW;4BACpC,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,kBACjB,iBAAiB,iBACjB,iBAAiB;wBAErB;oBACF;oBAEA,kEAAkE;oBAClE,IAAI,KAAK,KAAK,KAAK,oBAAoB;wBACrC,wBAAwB;wBACxB,IAAI,QAAQ,KAAK,CAAC,QAAQ,CAAC,mBAAmB;4BAC5C,OACE,iBAAiB,iBACjB,iBAAiB,mBACjB,iBAAiB,gBACjB,iBAAiB,qBACjB,iBAAiB;wBAErB;oBACF;oBAEA,sCAAsC;oBACtC,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,KAAK,KAAK,GAAG;YACf;YAEA,OAAO;QACT;QAEA,wFAAwF;QACxF,MAAM,SAAS,0BAA0B,MAAM,CAC7C,CAAC,OAAS,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG;QAG/C,uFAAuF;QACvF,IAAI,iBAAiB,kBAAkB;YACrC,OAAO;gBAAC;mBAAqB;aAAO;QACtC;QAEA,OAAO;IACT;IAEA,gEAAgE;IAChE,4EAA4E;IAC5E,kDAAkD;IAElD,8BAA8B;IAC9B,MAAM,OAAO;QACX,MAAM;YACJ,MAAM,MAAM,QAAQ;YACpB,OAAO,MAAM,SAAS;YACtB,QAAQ;QACV;QACA,OAAO;YACL;gBACE,MAAM,SAAS,IAAI;gBACnB,MAAM,yOAAA,CAAA,qBAAkB;gBACxB,MAAM,SAAS,IAAI;YACrB;SACD;QACD,oCAAoC;QACpC,SAAS;IAEX;IAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACrC,MAAM,cAAc,UAAU,eAAe,CAAC;IAE9C,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wCACA,eAAe;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,eAAe;8BACxC,cAAA,6LAAC,oIAAA,CAAA,UAAO;wBAAC,OAAO,KAAK,OAAO;;;;;;;;;;;;;;;;0BAGhC,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,eAAe;0BAChD,cAAA,6LAAC,oIAAA,CAAA,UAAO;oBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;AAIhC;GAr4BgB;;QACS,kJAAA,CAAA,iBAAc;QACpB,qIAAA,CAAA,cAAW;QA+2BA,sIAAA,CAAA,aAAU;;;KAj3BxB", "debugId": null}}, {"offset": {"line": 3910, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/team-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// import { GalleryVerticalEnd } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n\r\nexport function TeamHeader() {\r\n  const { data: user } = useCurrentUser();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <div className=\"bg-white text-primary flex aspect-square size-6 items-center justify-center rounded-lg\">\r\n        {/* <GalleryVerticalEnd className=\"size-3\" /> */}\r\n        <Image\r\n          src={\"/images/simbatelecomlogo.png\"}\r\n          alt=\"Simba Telecom Logo\"\r\n          className=\"object-contain\"\r\n          width={16}\r\n          height={16}\r\n        />\r\n      </div>\r\n      <div className=\"grid flex-1 text-left leading-tight\">\r\n        <span className=\"truncate text-sm font-semibold text-primary-foreground\">\r\n          {teamData.name}\r\n        </span>\r\n        <span className=\"truncate text-xs text-primary-foreground/90 font-medium\">\r\n          {teamData.plan}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,qDAAqD;AACrD;AACA;;;AAJA;;;AAMO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAEpC,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,WAAU;oBACV,OAAO;oBACP,QAAQ;;;;;;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;kCAEhB,6LAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;;;;;;;;;;;;;AAKxB;GAhEgB;;QACS,kJAAA,CAAA,iBAAc;;;KADvB", "debugId": null}}, {"offset": {"line": 4025, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/search-button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearch } from \"@/components/providers/search-provider\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\n\r\nexport function SearchButton() {\r\n  const { openSearch } = useSearch();\r\n\r\n  return (\r\n    <Button\r\n      variant=\"outline\"\r\n      size=\"sm\"\r\n      onClick={openSearch}\r\n      className=\"relative h-9 w-full justify-start bg-background text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-64\"\r\n    >\r\n      <span className=\"hidden lg:inline-flex\">Search anything...</span>\r\n      <span className=\"inline-flex lg:hidden\">Search...</span>\r\n      <kbd className=\"pointer-events-none absolute right-1.5 top-1.5 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex\">\r\n        <span className=\"text-xs\">⌘</span>K\r\n      </kbd>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD;IAE/B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,6LAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAU;;;;;;oBAAQ;;;;;;;;;;;;;AAI1C;GAjBgB;;QACS,wJAAA,CAAA,YAAS;;;KADlB", "debugId": null}}, {"offset": {"line": 4103, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/top-navigation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport {\r\n  Bell,\r\n  ChevronDown,\r\n  ChevronLeft,\r\n  LogOut,\r\n  Settings,\r\n  User,\r\n  PanelLeftIcon,\r\n  PanelRightIcon,\r\n  Home,\r\n} from \"lucide-react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser, useLogout } from \"@/features/auth/hooks/use-auth\";\r\nimport { TeamHeader } from \"@/components/team-header\";\r\nimport { SearchButton } from \"@/components/search-button\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport Link from \"next/link\";\r\n\r\nexport function TopNavigation() {\r\n  const { data: user } = useCurrentUser();\r\n  const logout = useLogout();\r\n  const { toggleSidebar, state, isMobile, setOpenMobile } = useSidebar();\r\n  const isExpanded = state === \"expanded\";\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Check if we're on the stock requests page\r\n  const isStockRequestsPage = pathname.includes('/inventory/stock-requests') &&\r\n    !pathname.includes('/create') &&\r\n    !pathname.match(/\\/inventory\\/stock-requests\\/\\d+/);\r\n\r\n  const handleLogout = () => {\r\n    logout.mutate();\r\n  };\r\n\r\n  const handleBackClick = () => {\r\n    router.back();\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-30 flex h-14 shrink-0 items-center gap-2 border-b border-primary-foreground/10 bg-primary text-primary-foreground transition-[width,height] ease-linear\">\r\n      <div className=\"flex flex-1 items-center justify-between px-4 w-full\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isMobile && isStockRequestsPage ? (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={handleBackClick}\r\n                aria-label=\"Go back\"\r\n                title=\"Go back\"\r\n              >\r\n                <ChevronLeft className=\"h-5 w-5\" />\r\n                <span className=\"sr-only\">Go Back</span>\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={() => (isMobile ? setOpenMobile(true) : toggleSidebar())}\r\n                data-mobile={isMobile ? \"true\" : \"false\"}\r\n                aria-label=\"Toggle sidebar\"\r\n                title={isExpanded ? \"Collapse sidebar\" : \"Expand sidebar\"}\r\n              >\r\n                {isExpanded ? (\r\n                  <PanelLeftIcon className=\"h-5 w-5\" />\r\n                ) : (\r\n                  <PanelRightIcon className=\"h-5 w-5\" />\r\n                )}\r\n                <span className=\"sr-only\">Toggle Sidebar</span>\r\n              </Button>\r\n            )}\r\n            <TeamHeader />\r\n\r\n            <div className=\"h-6 border-r border-primary-foreground/20 mx-2\"></div>\r\n\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n              asChild\r\n            >\r\n              <Link href=\"/dashboard\">\r\n                <Home className=\"h-4 w-4 mr-1\" />\r\n                Dashboard\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-center\">\r\n          <SearchButton />\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative h-8 w-8 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n            aria-label=\"Notifications\"\r\n          >\r\n            <Bell className=\"h-5 w-5\" />\r\n            <span className=\"absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-[10px] font-bold text-primary\">\r\n              3\r\n            </span>\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 gap-2 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                aria-label=\"Account\"\r\n              >\r\n                <Avatar className=\"h-7 w-7\">\r\n                  <AvatarImage\r\n                    src=\"/placeholder-avatar.jpg\"\r\n                    alt={user?.name || \"User\"}\r\n                  />\r\n                  <AvatarFallback className=\"text-xs font-semibold\">\r\n                    {user?.name ? user.name.charAt(0) : \"U\"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <span className=\"hidden text-sm font-semibold md:inline-block\">\r\n                  {user?.name || \"User\"}\r\n                </span>\r\n                <ChevronDown className=\"h-4 w-4 text-primary-foreground/80\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-56 rounded-lg\">\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-semibold leading-none\">\r\n                    {user?.name || \"User\"}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || \"<EMAIL>\"}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuGroup>\r\n                <DropdownMenuItem>\r\n                  <User className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Profile</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem>\r\n                  <Settings className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Settings</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuGroup>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span className=\"text-sm\">Log out</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAEA;AACA;AASA;AACA;AACA;;;AA/BA;;;;;;;;;;;AAiCO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACnE,MAAM,aAAa,UAAU;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,4CAA4C;IAC5C,MAAM,sBAAsB,SAAS,QAAQ,CAAC,gCAC5C,CAAC,SAAS,QAAQ,CAAC,cACnB,CAAC,SAAS,KAAK,CAAC;IAElB,MAAM,eAAe;QACnB,OAAO,MAAM;IACf;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI;IACb;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,YAAY,oCACX,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,cAAW;gCACX,OAAM;;kDAEN,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;qDAG5B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAO,WAAW,cAAc,QAAQ;gCACjD,eAAa,WAAW,SAAS;gCACjC,cAAW;gCACX,OAAO,aAAa,qBAAqB;;oCAExC,2BACC,6LAAC,uNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;6DAEzB,6LAAC,yNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAE5B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG9B,6LAAC,uIAAA,CAAA,aAAU;;;;;0CAEX,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,yIAAA,CAAA,eAAY;;;;;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,cAAW;;8CAEX,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAA6H;;;;;;;;;;;;sCAK/I,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,cAAW;;0DAEX,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDACV,KAAI;wDACJ,KAAK,MAAM,QAAQ;;;;;;kEAErB,6LAAC,qIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGxC,6LAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;0DAEjB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG3B,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,6LAAC,+IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ;;;;;;kEAEjB,6LAAC;wDAAE,WAAU;kEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,oBAAiB;;8DAChB,6LAAC,+IAAA,CAAA,mBAAgB;;sEACf,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC,+IAAA,CAAA,mBAAgB;;sEACf,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAG9B,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C;GAlJgB;;QACS,kJAAA,CAAA,iBAAc;QACtB,kJAAA,CAAA,YAAS;QACkC,sIAAA,CAAA,aAAU;QAErD,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KANd", "debugId": null}}, {"offset": {"line": 4555, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,6LAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;KAFS;AAIT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MAhBS;AAkBT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;MAhBS;AAkBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,mNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAhBS", "debugId": null}}, {"offset": {"line": 4705, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/breadcrumbs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from \"@/components/ui/breadcrumb\";\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\n\r\nexport function Breadcrumbs() {\r\n  const pathname = usePathname();\r\n\r\n  // Get the current page name from the pathname\r\n  const getPageName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length === 0) return \"Dashboard\";\r\n\r\n    // Get the last segment of the path and capitalize it\r\n    const lastSegment = path[path.length - 1];\r\n    return (\r\n      lastSegment.charAt(0).toUpperCase() +\r\n      lastSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  // Get the parent path for breadcrumb\r\n  const getParentPath = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Remove the last segment to get the parent path\r\n    return \"/\" + path.slice(0, path.length - 1).join(\"/\");\r\n  };\r\n\r\n  // Get the parent name for breadcrumb\r\n  const getParentName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Get the second-to-last segment and capitalize it\r\n    const parentSegment = path[path.length - 2];\r\n    return (\r\n      parentSegment.charAt(0).toUpperCase() +\r\n      parentSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  const parentPath = getParentPath();\r\n  const parentName = getParentName();\r\n  const pageName = getPageName();\r\n\r\n  return (\r\n    <div className=\"mb-4\">\r\n      <Breadcrumb>\r\n        <BreadcrumbList>\r\n          {parentPath && parentName && (\r\n            <>\r\n              <BreadcrumbItem className=\"hidden md:block\">\r\n                <BreadcrumbLink asChild>\r\n                  <NavigationLink\r\n                    href={parentPath}\r\n                    className=\"text-sm font-medium text-muted-foreground hover:text-foreground\"\r\n                  >\r\n                    {parentName}\r\n                  </NavigationLink>\r\n                </BreadcrumbLink>\r\n              </BreadcrumbItem>\r\n              <BreadcrumbSeparator className=\"hidden md:block h-3 w-3\" />\r\n            </>\r\n          )}\r\n          <BreadcrumbItem>\r\n            <BreadcrumbPage className=\"text-sm font-semibold\">\r\n              {pageName}\r\n            </BreadcrumbPage>\r\n          </BreadcrumbItem>\r\n        </BreadcrumbList>\r\n      </Breadcrumb>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;;;AAXA;;;;AAaO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,cAAc;QAClB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAE9B,qDAAqD;QACrD,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,OACE,YAAY,MAAM,CAAC,GAAG,WAAW,KACjC,YAAY,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEvC;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,iDAAiD;QACjD,OAAO,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC;IACnD;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,mDAAmD;QACnD,MAAM,gBAAgB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC3C,OACE,cAAc,MAAM,CAAC,GAAG,WAAW,KACnC,cAAc,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEzC;IAEA,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,WAAW;IAEjB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,yIAAA,CAAA,aAAU;sBACT,cAAA,6LAAC,yIAAA,CAAA,iBAAc;;oBACZ,cAAc,4BACb;;0CACE,6LAAC,yIAAA,CAAA,iBAAc;gCAAC,WAAU;0CACxB,cAAA,6LAAC,yIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC,iJAAA,CAAA,iBAAc;wCACb,MAAM;wCACN,WAAU;kDAET;;;;;;;;;;;;;;;;0CAIP,6LAAC,yIAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;kCAGnC,6LAAC,yIAAA,CAAA,iBAAc;kCACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAtEgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 4834, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/main-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { ReactNode, Suspense, useEffect } from \"react\";\r\nimport { RoleGuard } from \"./role-guard\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\nimport { SidebarProvider, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { AppSidebar } from \"@/components/app-sidebar\";\r\nimport { TopNavigation } from \"@/components/top-navigation\";\r\nimport { Breadcrumbs } from \"@/components/breadcrumbs\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\";\r\n\r\ninterface MainLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function MainLayout({ children }: MainLayoutProps) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Use useEffect for redirects to avoid hydration issues\r\n  useEffect(() => {\r\n    // Only show loading on the very first render if:\r\n    // 1. Auth is not initialized yet\r\n    // 2. We haven't shown the loading screen before in this session\r\n    if (!isInitialized && !hasShownLoading.main) {\r\n      setLoading(\"main\", true);\r\n    } else {\r\n      setLoading(\"main\", false);\r\n    }\r\n\r\n    if (!isInitialized) {\r\n      return; // Wait until auth state is initialized\r\n    }\r\n\r\n    // After auth state is initialized, we can hide the loading screen\r\n    setLoading(\"main\", false);\r\n\r\n    // If we have an access token, mark that we've shown loading\r\n    // This prevents showing loading on subsequent navigations\r\n    if (accessToken) {\r\n      markLoadingShown(\"main\");\r\n    }\r\n\r\n    // We're disabling client-side redirects to avoid redirect loops\r\n    // The middleware will handle redirects instead\r\n    if (!accessToken) {\r\n      // We're not redirecting here anymore\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [accessToken, isInitialized, hasShownLoading.main]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in loading state\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (isLoading.main) {\r\n        setLoading(\"main\", false);\r\n        markLoadingShown(\"main\");\r\n      }\r\n    }, 1500); // 1.5 second timeout for better UX\r\n\r\n    return () => clearTimeout(timeoutId);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.main]);\r\n\r\n  // Don't render anything if not authenticated\r\n  if (!accessToken) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider>\r\n      <RoleGuard>\r\n        <MainLayoutContent>{children}</MainLayoutContent>\r\n      </RoleGuard>\r\n    </SidebarProvider>\r\n  );\r\n}\r\n\r\nfunction MainLayoutContent({ children }: { children: ReactNode }) {\r\n  const { state, openMobile, setOpenMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-screen w-full bg-gray-50\">\r\n      <TopNavigation />\r\n      <div className=\"flex h-[calc(100vh-3.5rem)] overflow-hidden\">\r\n        {/* Desktop sidebar */}\r\n        <aside\r\n          className={cn(\r\n            \"border-r bg-white transition-all duration-300 shrink-0 hidden md:block\",\r\n            isCollapsed ? \"w-16\" : \"w-64\"\r\n          )}\r\n        >\r\n          <AppSidebar />\r\n        </aside>\r\n\r\n        {/* Mobile sidebar */}\r\n        <div className=\"md:hidden\">\r\n          <Sheet open={openMobile} onOpenChange={setOpenMobile}>\r\n            <SheetContent\r\n              side=\"left\"\r\n              className=\"p-0 w-[280px] border-r bg-white\"\r\n            >\r\n              <div className=\"h-full overflow-y-auto\">\r\n                <AppSidebar />\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        <main className=\"flex flex-col w-full overflow-y-auto overflow-x-hidden\">\r\n          <div className=\"p-6\">\r\n            <Suspense fallback={<div>Loading content...</div>}>\r\n              <Breadcrumbs />\r\n              {children}\r\n            </Suspense>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;AAoBO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;IAEX,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,iDAAiD;YACjD,iCAAiC;YACjC,gEAAgE;YAChE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;gBAC3C,WAAW,QAAQ;YACrB,OAAO;gBACL,WAAW,QAAQ;YACrB;YAEA,IAAI,CAAC,eAAe;gBAClB,QAAQ,uCAAuC;YACjD;YAEA,kEAAkE;YAClE,WAAW,QAAQ;YAEnB,4DAA4D;YAC5D,0DAA0D;YAC1D,IAAI,aAAa;gBACf,iBAAiB;YACnB;YAEA,gEAAgE;YAChE,+CAA+C;YAC/C,IAAI,CAAC,aAAa;YAChB,qCAAqC;YACvC;QACA,uDAAuD;QACzD;+BAAG;QAAC;QAAa;QAAe,gBAAgB,IAAI;KAAC;IAErD,iEAAiE;IACjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,YAAY;kDAAW;oBAC3B,IAAI,UAAU,IAAI,EAAE;wBAClB,WAAW,QAAQ;wBACnB,iBAAiB;oBACnB;gBACF;iDAAG,OAAO,mCAAmC;YAE7C;wCAAO,IAAM,aAAa;;QAC1B,uDAAuD;QACzD;+BAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6CAA6C;IAC7C,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,iJAAA,CAAA,YAAS;sBACR,cAAA,6LAAC;0BAAmB;;;;;;;;;;;;;;;;AAI5B;GA9DgB;;QACyB,wIAAA,CAAA,gBAAa;QAElD,yJAAA,CAAA,aAAU;;;KAHE;AAgEhB,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;;IAC9D,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,cAAc,UAAU;IAE9B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,cAAc,SAAS;kCAGzB,cAAA,6LAAC,uIAAA,CAAA,aAAU;;;;;;;;;;kCAIb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;sCACrC,cAAA,6LAAC,oIAAA,CAAA,eAAY;gCACX,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6JAAA,CAAA,WAAQ;gCAAC,wBAAU,6LAAC;8CAAI;;;;;;;kDACvB,6LAAC,oIAAA,CAAA,cAAW;;;;;oCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;IA3CS;;QACsC,sIAAA,CAAA,aAAU;;;MADhD", "debugId": null}}, {"offset": {"line": 5076, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        success:\r\n          \"border-transparent bg-green-500 text-white [a&]:hover:bg-green-600 focus-visible:ring-green-500/20 dark:focus-visible:ring-green-500/40\",\r\n        warning:\r\n          \"border-transparent bg-yellow-500 text-white [a&]:hover:bg-yellow-600 focus-visible:ring-yellow-500/20 dark:focus-visible:ring-yellow-500/40\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 5130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport {\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n  MoreHorizontalIcon,\r\n} from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button, buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Pagination({ className, ...props }: React.ComponentProps<\"nav\">) {\r\n  return (\r\n    <nav\r\n      role=\"navigation\"\r\n      aria-label=\"pagination\"\r\n      data-slot=\"pagination\"\r\n      className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"pagination-content\"\r\n      className={cn(\"flex flex-row items-center gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationItem({ ...props }: React.ComponentProps<\"li\">) {\r\n  return <li data-slot=\"pagination-item\" {...props} />\r\n}\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<React.ComponentProps<typeof Button>, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nfunction PaginationLink({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) {\r\n  return (\r\n    <a\r\n      aria-current={isActive ? \"page\" : undefined}\r\n      data-slot=\"pagination-link\"\r\n      data-active={isActive}\r\n      className={cn(\r\n        buttonVariants({\r\n          variant: isActive ? \"outline\" : \"ghost\",\r\n          size,\r\n        }),\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationPrevious({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to previous page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pl-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <ChevronLeftIcon />\r\n      <span className=\"hidden sm:block\">Previous</span>\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationNext({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to next page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pr-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <span className=\"hidden sm:block\">Next</span>\r\n      <ChevronRightIcon />\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      aria-hidden\r\n      data-slot=\"pagination-ellipsis\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontalIcon className=\"size-4\" />\r\n      <span className=\"sr-only\">More pages</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationLink,\r\n  PaginationItem,\r\n  PaginationPrevious,\r\n  PaginationNext,\r\n  PaginationEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AAAA;AAAA;AAMA;AACA;;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,MAAK;QACL,cAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EAAE,GAAG,OAAmC;IAC9D,qBAAO,6LAAC;QAAG,aAAU;QAAmB,GAAG,KAAK;;;;;;AAClD;MAFS;AAST,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACb,GAAG,OACiB;IACpB,qBACE,6LAAC;QACC,gBAAc,WAAW,SAAS;QAClC,aAAU;QACV,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,YAAY;YAChC;QACF,IACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,6LAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,6LAAC,2NAAA,CAAA,kBAAe;;;;;0BAChB,6LAAC;gBAAK,WAAU;0BAAkB;;;;;;;;;;;;AAGxC;MAfS;AAiBT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,6LAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BAAkB;;;;;;0BAClC,6LAAC,6NAAA,CAAA,mBAAgB;;;;;;;;;;;AAGvB;MAfS;AAiBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;0BAC9B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAfS", "debugId": null}}, {"offset": {"line": 5307, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 5556, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/data-pagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo } from \"react\";\r\nimport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from \"@/components/ui/pagination\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ChevronFirst, ChevronLast } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nexport interface DataPaginationProps {\r\n  // Required props\r\n  currentPage: number;\r\n  totalPages: number;\r\n  onPageChange: (page: number) => void;\r\n\r\n  // Optional props\r\n  pageSize?: number;\r\n  pageSizes?: number[];\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  totalItems?: number;\r\n  isLoading?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showItemsInfo?: boolean;\r\n  showFirstLastButtons?: boolean;\r\n  maxPageButtons?: number;\r\n  className?: string;\r\n  compact?: boolean;\r\n  ariaLabel?: string;\r\n}\r\n\r\nexport function DataPagination({\r\n  currentPage,\r\n  totalPages,\r\n  onPageChange,\r\n  pageSize = 10,\r\n  pageSizes = [10, 25, 50, 100, 250, 500, 1000],\r\n  onPageSizeChange,\r\n  totalItems,\r\n  isLoading = false,\r\n  showPageSizeSelector = true,\r\n  showItemsInfo = true,\r\n  showFirstLastButtons = true,\r\n  maxPageButtons = 5,\r\n  className,\r\n  compact = false,\r\n  ariaLabel = \"Pagination\",\r\n}: DataPaginationProps) {\r\n  // Don't render pagination if there's only one page and no page size selector\r\n  if (totalPages <= 1 && (!showPageSizeSelector || !onPageSizeChange)) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate the current range of items being displayed\r\n  const itemRange = useMemo(() => {\r\n    if (!totalItems) return null;\r\n\r\n    const start = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;\r\n    const end = Math.min(currentPage * pageSize, totalItems);\r\n\r\n    return { start, end };\r\n  }, [currentPage, pageSize, totalItems]);\r\n\r\n  // Generate page numbers to display\r\n  const pageNumbers = useMemo(() => {\r\n    const numbers: (number | string)[] = [];\r\n\r\n    if (totalPages <= maxPageButtons) {\r\n      // If total pages is less than or equal to maxPageButtons, show all pages\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        numbers.push(i);\r\n      }\r\n    } else {\r\n      // Always include first page\r\n      numbers.push(1);\r\n\r\n      // Calculate start and end of page numbers to show\r\n      let start = Math.max(2, currentPage - Math.floor(maxPageButtons / 2) + 1);\r\n      let end = Math.min(totalPages - 1, start + maxPageButtons - 3);\r\n\r\n      // Adjust if we're at the beginning\r\n      if (currentPage <= Math.floor(maxPageButtons / 2)) {\r\n        end = maxPageButtons - 2;\r\n        start = 2;\r\n      }\r\n\r\n      // Adjust if we're at the end\r\n      if (currentPage > totalPages - Math.floor(maxPageButtons / 2)) {\r\n        start = Math.max(2, totalPages - maxPageButtons + 2);\r\n        end = totalPages - 1;\r\n      }\r\n\r\n      // Add ellipsis before middle pages if needed\r\n      if (start > 2) {\r\n        numbers.push(\"ellipsis-start\");\r\n      }\r\n\r\n      // Add middle pages\r\n      for (let i = start; i <= end; i++) {\r\n        numbers.push(i);\r\n      }\r\n\r\n      // Add ellipsis after middle pages if needed\r\n      if (end < totalPages - 1) {\r\n        numbers.push(\"ellipsis-end\");\r\n      }\r\n\r\n      // Always include last page if more than one page\r\n      if (totalPages > 1) {\r\n        numbers.push(totalPages);\r\n      }\r\n    }\r\n\r\n    return numbers;\r\n  }, [currentPage, totalPages, maxPageButtons]);\r\n\r\n  // Handle page click with validation\r\n  const handlePageClick = (page: number, e: React.MouseEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Only trigger page change if it's not the current page and not loading\r\n    if (page !== currentPage && !isLoading) {\r\n      // Force the page to be within valid range\r\n      const validPage = Math.max(1, Math.min(page, totalPages));\r\n      onPageChange(validPage);\r\n    }\r\n  };\r\n\r\n  // Handle page size change\r\n  const handlePageSizeChange = (value: string) => {\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(parseInt(value, 10));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex flex-col gap-4 w-full\",\r\n        compact\r\n          ? \"sm:flex-row sm:items-center sm:justify-between\"\r\n          : \"md:flex-row md:items-center md:justify-between\",\r\n        className\r\n      )}\r\n      aria-label={ariaLabel}\r\n    >\r\n      {/* Page size selector */}\r\n      {showPageSizeSelector && onPageSizeChange && (\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-sm text-muted-foreground\">Show</span>\r\n          <Select\r\n            value={pageSize.toString()}\r\n            onValueChange={handlePageSizeChange}\r\n            disabled={isLoading}\r\n          >\r\n            <SelectTrigger\r\n              className=\"h-8 w-[70px]\"\r\n              aria-label=\"Select page size\"\r\n            >\r\n              <SelectValue placeholder={pageSize.toString()} />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              {pageSizes.map((size) => (\r\n                <SelectItem key={size} value={size.toString()}>\r\n                  {size}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n          <span className=\"text-sm text-muted-foreground\">per page</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Pagination controls - Always show if we have pagination data */}\r\n      {totalPages >= 1 && (\r\n        <div className=\"bg-muted/50 rounded-md p-1\">\r\n          <Pagination>\r\n            <PaginationContent className=\"flex-wrap justify-center\">\r\n              {/* First page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(1, e)}\r\n                    disabled={currentPage === 1 || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to first page\"\r\n                  >\r\n                    <ChevronFirst className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n\r\n              {/* Previous page button */}\r\n              <PaginationItem>\r\n                <PaginationPrevious\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage - 1, e)}\r\n                  aria-disabled={currentPage === 1 || isLoading}\r\n                  className={cn(\r\n                    currentPage === 1 || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Page numbers */}\r\n              {!compact &&\r\n                pageNumbers.map((pageNumber, index) => {\r\n                  if (typeof pageNumber === \"string\") {\r\n                    return (\r\n                      <PaginationItem key={pageNumber}>\r\n                        <PaginationEllipsis />\r\n                      </PaginationItem>\r\n                    );\r\n                  }\r\n\r\n                  return (\r\n                    <PaginationItem key={`page-${pageNumber}`}>\r\n                      <PaginationLink\r\n                        href=\"#\"\r\n                        onClick={(e) => handlePageClick(pageNumber, e)}\r\n                        isActive={currentPage === pageNumber}\r\n                        aria-current={\r\n                          currentPage === pageNumber ? \"page\" : undefined\r\n                        }\r\n                      >\r\n                        {pageNumber}\r\n                      </PaginationLink>\r\n                    </PaginationItem>\r\n                  );\r\n                })}\r\n\r\n              {/* Compact view shows current/total instead of page numbers */}\r\n              {compact && (\r\n                <div className=\"flex items-center mx-2\">\r\n                  <span className=\"text-sm\">\r\n                    Page <span className=\"font-bold\">{currentPage}</span> of{\" \"}\r\n                    <span className=\"font-medium\">{totalPages}</span>\r\n                    {isLoading && (\r\n                      <span className=\"ml-1 text-muted-foreground\">\r\n                        (Loading...)\r\n                      </span>\r\n                    )}\r\n                  </span>\r\n                </div>\r\n              )}\r\n\r\n              {/* Next page button */}\r\n              <PaginationItem>\r\n                <PaginationNext\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage + 1, e)}\r\n                  aria-disabled={currentPage === totalPages || isLoading}\r\n                  className={cn(\r\n                    currentPage === totalPages || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Last page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(totalPages, e)}\r\n                    disabled={currentPage === totalPages || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to last page\"\r\n                  >\r\n                    <ChevronLast className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n            </PaginationContent>\r\n          </Pagination>\r\n        </div>\r\n      )}\r\n\r\n      {/* Items info */}\r\n      {showItemsInfo && totalItems !== undefined && (\r\n        <div className=\"text-sm text-muted-foreground whitespace-nowrap\">\r\n          {totalItems === 0 ? (\r\n            \"No items\"\r\n          ) : (\r\n            <>\r\n              Showing {itemRange?.start} to {itemRange?.end} of {totalItems}{\" \"}\r\n              items\r\n            </>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AAOA;AACA;AAAA;AACA;;;AArBA;;;;;;;AA4CO,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,YAAY,EACZ,WAAW,EAAE,EACb,YAAY;IAAC;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;CAAK,EAC7C,gBAAgB,EAChB,UAAU,EACV,YAAY,KAAK,EACjB,uBAAuB,IAAI,EAC3B,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,iBAAiB,CAAC,EAClB,SAAS,EACT,UAAU,KAAK,EACf,YAAY,YAAY,EACJ;;IACpB,6EAA6E;IAC7E,IAAI,cAAc,KAAK,CAAC,CAAC,wBAAwB,CAAC,gBAAgB,GAAG;QACnE,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE;YACxB,IAAI,CAAC,YAAY,OAAO;YAExB,MAAM,QAAQ,eAAe,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,WAAW;YACpE,MAAM,MAAM,KAAK,GAAG,CAAC,cAAc,UAAU;YAE7C,OAAO;gBAAE;gBAAO;YAAI;QACtB;4CAAG;QAAC;QAAa;QAAU;KAAW;IAEtC,mCAAmC;IACnC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YAC1B,MAAM,UAA+B,EAAE;YAEvC,IAAI,cAAc,gBAAgB;gBAChC,yEAAyE;gBACzE,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;oBACpC,QAAQ,IAAI,CAAC;gBACf;YACF,OAAO;gBACL,4BAA4B;gBAC5B,QAAQ,IAAI,CAAC;gBAEb,kDAAkD;gBAClD,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB,KAAK;gBACvE,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa,GAAG,QAAQ,iBAAiB;gBAE5D,mCAAmC;gBACnC,IAAI,eAAe,KAAK,KAAK,CAAC,iBAAiB,IAAI;oBACjD,MAAM,iBAAiB;oBACvB,QAAQ;gBACV;gBAEA,6BAA6B;gBAC7B,IAAI,cAAc,aAAa,KAAK,KAAK,CAAC,iBAAiB,IAAI;oBAC7D,QAAQ,KAAK,GAAG,CAAC,GAAG,aAAa,iBAAiB;oBAClD,MAAM,aAAa;gBACrB;gBAEA,6CAA6C;gBAC7C,IAAI,QAAQ,GAAG;oBACb,QAAQ,IAAI,CAAC;gBACf;gBAEA,mBAAmB;gBACnB,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;oBACjC,QAAQ,IAAI,CAAC;gBACf;gBAEA,4CAA4C;gBAC5C,IAAI,MAAM,aAAa,GAAG;oBACxB,QAAQ,IAAI,CAAC;gBACf;gBAEA,iDAAiD;gBACjD,IAAI,aAAa,GAAG;oBAClB,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,OAAO;QACT;8CAAG;QAAC;QAAa;QAAY;KAAe;IAE5C,oCAAoC;IACpC,MAAM,kBAAkB,CAAC,MAAc;QACrC,EAAE,cAAc;QAEhB,wEAAwE;QACxE,IAAI,SAAS,eAAe,CAAC,WAAW;YACtC,0CAA0C;YAC1C,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;YAC7C,aAAa;QACf;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,IAAI,kBAAkB;YACpB,iBAAiB,SAAS,OAAO;QACnC;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,UACI,mDACA,kDACJ;QAEF,cAAY;;YAGX,wBAAwB,kCACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAgC;;;;;;kCAChD,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,QAAQ;wBACxB,eAAe;wBACf,UAAU;;0CAEV,6LAAC,qIAAA,CAAA,gBAAa;gCACZ,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAa,SAAS,QAAQ;;;;;;;;;;;0CAE7C,6LAAC,qIAAA,CAAA,gBAAa;0CACX,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,qIAAA,CAAA,aAAU;wCAAY,OAAO,KAAK,QAAQ;kDACxC;uCADc;;;;;;;;;;;;;;;;kCAMvB,6LAAC;wBAAK,WAAU;kCAAgC;;;;;;;;;;;;YAKnD,cAAc,mBACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,yIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,yIAAA,CAAA,oBAAiB;wBAAC,WAAU;;4BAE1B,sCACC,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,GAAG;oCACnC,UAAU,gBAAgB,KAAK;oCAC/B,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAM9B,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,yIAAA,CAAA,qBAAkB;oCACjB,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,KAAK;oCACpC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,KAAK,YACjB,mCACA,IACJ;;;;;;;;;;;4BAML,CAAC,WACA,YAAY,GAAG,CAAC,CAAC,YAAY;gCAC3B,IAAI,OAAO,eAAe,UAAU;oCAClC,qBACE,6LAAC,yIAAA,CAAA,iBAAc;kDACb,cAAA,6LAAC,yIAAA,CAAA,qBAAkB;;;;;uCADA;;;;;gCAIzB;gCAEA,qBACE,6LAAC,yIAAA,CAAA,iBAAc;8CACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;wCACb,MAAK;wCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;wCAC5C,UAAU,gBAAgB;wCAC1B,gBACE,gBAAgB,aAAa,SAAS;kDAGvC;;;;;;mCATgB,CAAC,KAAK,EAAE,YAAY;;;;;4BAa7C;4BAGD,yBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAU;sDACnB,6LAAC;4CAAK,WAAU;sDAAa;;;;;;wCAAmB;wCAAI;sDACzD,6LAAC;4CAAK,WAAU;sDAAe;;;;;;wCAC9B,2BACC,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;0CASrD,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;oCACb,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,cAAc;oCAC7C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,cAAc,YAC1B,mCACA,IACJ;;;;;;;;;;;4BAML,sCACC,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;oCAC5C,UAAU,gBAAgB,cAAc;oCACxC,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUpC,iBAAiB,eAAe,2BAC/B,6LAAC;gBAAI,WAAU;0BACZ,eAAe,IACd,2BAEA;;wBAAE;wBACS,WAAW;wBAAM;wBAAK,WAAW;wBAAI;wBAAK;wBAAY;wBAAI;;;;;;;;;;;;;;AAQjF;GA9QgB;KAAA", "debugId": null}}, {"offset": {"line": 5958, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-debounced-search.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\n\r\ninterface UseDebouncedSearchOptions {\r\n  initialValue?: string;\r\n  delay?: number;\r\n  minLength?: number;\r\n  onSearch?: (value: string) => void;\r\n  onTyping?: (value: string) => void;\r\n  onClear?: () => void;\r\n}\r\n\r\n/**\r\n * Hook for debounced search input\r\n *\r\n * @param options Configuration options\r\n * @returns Search state and handlers\r\n */\r\nexport function useDebouncedSearch({\r\n  initialValue = \"\",\r\n  delay = 300,\r\n  minLength = 0,\r\n  onSearch,\r\n  onTyping,\r\n  onClear,\r\n}: UseDebouncedSearchOptions = {}) {\r\n  const [inputValue, setInputValue] = useState(initialValue);\r\n  const [debouncedValue, setDebouncedValue] = useState(initialValue);\r\n  const [isSearching, setIsSearching] = useState(false);\r\n  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);\r\n  const previousValueRef = useRef(initialValue);\r\n\r\n  // Clear the debounce timer on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (debounceTimerRef.current) {\r\n        clearTimeout(debounceTimerRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Update debounced value after delay\r\n  useEffect(() => {\r\n    // Skip if the value hasn't changed\r\n    if (inputValue === previousValueRef.current) {\r\n      return;\r\n    }\r\n\r\n    // Call onTyping callback immediately\r\n    if (onTyping) {\r\n      onTyping(inputValue);\r\n    }\r\n\r\n    // Clear any existing timer\r\n    if (debounceTimerRef.current) {\r\n      clearTimeout(debounceTimerRef.current);\r\n    }\r\n\r\n    // Set searching state\r\n    setIsSearching(inputValue.length >= minLength);\r\n\r\n    // Set a new timer\r\n    debounceTimerRef.current = setTimeout(() => {\r\n      setDebouncedValue(inputValue);\r\n      previousValueRef.current = inputValue;\r\n\r\n      // Only trigger search if value meets minimum length\r\n      if (inputValue.length >= minLength) {\r\n        if (onSearch) {\r\n          onSearch(inputValue);\r\n        }\r\n      } else if (inputValue === \"\" && onClear) {\r\n        onClear();\r\n      }\r\n\r\n      setIsSearching(false);\r\n    }, delay);\r\n  }, [inputValue, delay, minLength, onSearch, onTyping, onClear]);\r\n\r\n  // Handle input change\r\n  const handleChange = useCallback((value: string) => {\r\n    setInputValue(value);\r\n  }, []);\r\n\r\n  // Clear search\r\n  const clearSearch = useCallback(() => {\r\n    setInputValue(\"\");\r\n    setDebouncedValue(\"\");\r\n    previousValueRef.current = \"\";\r\n\r\n    if (onClear) {\r\n      onClear();\r\n    }\r\n  }, [onClear]);\r\n\r\n  // Reset to initial value\r\n  const resetSearch = useCallback(() => {\r\n    setInputValue(initialValue);\r\n    setDebouncedValue(initialValue);\r\n    previousValueRef.current = initialValue;\r\n\r\n    if (initialValue === \"\" && onClear) {\r\n      onClear();\r\n    } else if (initialValue.length >= minLength && onSearch) {\r\n      onSearch(initialValue);\r\n    }\r\n  }, [initialValue, minLength, onSearch, onClear]);\r\n\r\n  // Force immediate search\r\n  const forceSearch = useCallback(() => {\r\n    if (debounceTimerRef.current) {\r\n      clearTimeout(debounceTimerRef.current);\r\n    }\r\n\r\n    setDebouncedValue(inputValue);\r\n    previousValueRef.current = inputValue;\r\n\r\n    if (inputValue.length >= minLength) {\r\n      if (onSearch) {\r\n        onSearch(inputValue);\r\n      }\r\n    } else if (inputValue === \"\" && onClear) {\r\n      onClear();\r\n    }\r\n\r\n    setIsSearching(false);\r\n  }, [inputValue, minLength, onSearch, onClear]);\r\n\r\n  return {\r\n    inputValue,\r\n    debouncedValue,\r\n    isSearching,\r\n    handleChange,\r\n    clearSearch,\r\n    resetSearch,\r\n    setInputValue,\r\n    forceSearch,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAmBO,SAAS,mBAAmB,EACjC,eAAe,EAAE,EACjB,QAAQ,GAAG,EACX,YAAY,CAAC,EACb,QAAQ,EACR,QAAQ,EACR,OAAO,EACmB,GAAG,CAAC,CAAC;;IAC/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACvD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;gDAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;uCAAG,EAAE;IAEL,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,mCAAmC;YACnC,IAAI,eAAe,iBAAiB,OAAO,EAAE;gBAC3C;YACF;YAEA,qCAAqC;YACrC,IAAI,UAAU;gBACZ,SAAS;YACX;YAEA,2BAA2B;YAC3B,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,sBAAsB;YACtB,eAAe,WAAW,MAAM,IAAI;YAEpC,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;gDAAW;oBACpC,kBAAkB;oBAClB,iBAAiB,OAAO,GAAG;oBAE3B,oDAAoD;oBACpD,IAAI,WAAW,MAAM,IAAI,WAAW;wBAClC,IAAI,UAAU;4BACZ,SAAS;wBACX;oBACF,OAAO,IAAI,eAAe,MAAM,SAAS;wBACvC;oBACF;oBAEA,eAAe;gBACjB;+CAAG;QACL;uCAAG;QAAC;QAAY;QAAO;QAAW;QAAU;QAAU;KAAQ;IAE9D,sBAAsB;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YAChC,cAAc;QAChB;uDAAG,EAAE;IAEL,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC9B,cAAc;YACd,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;YAE3B,IAAI,SAAS;gBACX;YACF;QACF;sDAAG;QAAC;KAAQ;IAEZ,yBAAyB;IACzB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC9B,cAAc;YACd,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;YAE3B,IAAI,iBAAiB,MAAM,SAAS;gBAClC;YACF,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,UAAU;gBACvD,SAAS;YACX;QACF;sDAAG;QAAC;QAAc;QAAW;QAAU;KAAQ;IAE/C,yBAAyB;IACzB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC9B,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;YAE3B,IAAI,WAAW,MAAM,IAAI,WAAW;gBAClC,IAAI,UAAU;oBACZ,SAAS;gBACX;YACF,OAAO,IAAI,eAAe,MAAM,SAAS;gBACvC;YACF;YAEA,eAAe;QACjB;sDAAG;QAAC;QAAY;QAAW;QAAU;KAAQ;IAE7C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAxHgB", "debugId": null}}, {"offset": {"line": 6107, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/search-input.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useCallback, forwardRef } from \"react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Search, X } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useDebouncedSearch } from \"@/hooks/use-debounced-search\";\r\n\r\nexport interface SearchInputProps {\r\n  /**\r\n   * Placeholder text for the search input\r\n   */\r\n  placeholder?: string;\r\n  /**\r\n   * Initial search value\r\n   */\r\n  value?: string;\r\n  /**\r\n   * Search callback function\r\n   */\r\n  onSearch?: (query: string) => void;\r\n  /**\r\n   * Clear callback function\r\n   */\r\n  onClear?: () => void;\r\n  /**\r\n   * Debounce delay in milliseconds\r\n   */\r\n  debounceMs?: number;\r\n  /**\r\n   * Minimum length before triggering search\r\n   */\r\n  minLength?: number;\r\n  /**\r\n   * Whether to show search button\r\n   */\r\n  showSearchButton?: boolean;\r\n  /**\r\n   * Whether to show clear button\r\n   */\r\n  showClearButton?: boolean;\r\n  /**\r\n   * Search mode: 'debounced' | 'manual' | 'realtime'\r\n   */\r\n  mode?: \"debounced\" | \"manual\" | \"realtime\";\r\n  /**\r\n   * Additional class names\r\n   */\r\n  className?: string;\r\n  /**\r\n   * Input class names\r\n   */\r\n  inputClassName?: string;\r\n  /**\r\n   * Button class names\r\n   */\r\n  buttonClassName?: string;\r\n  /**\r\n   * Whether the search is loading\r\n   */\r\n  isLoading?: boolean;\r\n  /**\r\n   * Disabled state\r\n   */\r\n  disabled?: boolean;\r\n  /**\r\n   * Size variant\r\n   */\r\n  size?: \"sm\" | \"default\" | \"lg\";\r\n}\r\n\r\n/**\r\n * Standardized search input component with consistent behavior\r\n */\r\nexport const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(\r\n  (\r\n    {\r\n      placeholder = \"Search...\",\r\n      value = \"\",\r\n      onSearch,\r\n      onClear,\r\n      debounceMs = 300,\r\n      minLength = 0,\r\n      showSearchButton = false,\r\n      showClearButton = true,\r\n      mode = \"debounced\",\r\n      className,\r\n      inputClassName,\r\n      buttonClassName,\r\n      isLoading = false,\r\n      disabled = false,\r\n      size = \"default\",\r\n    },\r\n    ref\r\n  ) => {\r\n    const [manualValue, setManualValue] = useState(value);\r\n\r\n    // Use debounced search for debounced mode\r\n    const {\r\n      inputValue,\r\n      handleChange,\r\n      clearSearch,\r\n      forceSearch,\r\n      isSearching,\r\n    } = useDebouncedSearch({\r\n      initialValue: value,\r\n      delay: debounceMs,\r\n      minLength,\r\n      onSearch: mode === \"debounced\" ? onSearch : undefined,\r\n      onClear,\r\n    });\r\n\r\n    // Handle different search modes\r\n    const currentValue = mode === \"manual\" ? manualValue : inputValue;\r\n    const currentIsLoading = mode === \"debounced\" ? isSearching : isLoading;\r\n\r\n    const handleInputChange = useCallback(\r\n      (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const newValue = e.target.value;\r\n\r\n        if (mode === \"manual\") {\r\n          setManualValue(newValue);\r\n        } else if (mode === \"realtime\") {\r\n          onSearch?.(newValue);\r\n        } else {\r\n          handleChange(newValue);\r\n        }\r\n      },\r\n      [mode, handleChange, onSearch]\r\n    );\r\n\r\n    const handleSearchClick = useCallback(() => {\r\n      if (mode === \"manual\") {\r\n        onSearch?.(manualValue);\r\n      } else {\r\n        forceSearch();\r\n      }\r\n    }, [mode, manualValue, onSearch, forceSearch]);\r\n\r\n    const handleClearClick = useCallback(() => {\r\n      if (mode === \"manual\") {\r\n        setManualValue(\"\");\r\n        onClear?.();\r\n      } else {\r\n        clearSearch();\r\n      }\r\n    }, [mode, clearSearch, onClear]);\r\n\r\n    const handleKeyDown = useCallback(\r\n      (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n        if (e.key === \"Enter\") {\r\n          e.preventDefault();\r\n          handleSearchClick();\r\n        }\r\n      },\r\n      [handleSearchClick]\r\n    );\r\n\r\n    const sizeClasses = {\r\n      sm: \"h-8 text-sm\",\r\n      default: \"h-9\",\r\n      lg: \"h-10\",\r\n    };\r\n\r\n    const buttonSizeClasses = {\r\n      sm: \"h-8 px-2\",\r\n      default: \"h-9 px-3\",\r\n      lg: \"h-10 px-4\",\r\n    };\r\n\r\n    return (\r\n      <div className={cn(\"flex items-center gap-2\", className)}>\r\n        <div className=\"relative flex-1\">\r\n          <Search className=\"absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            ref={ref}\r\n            type=\"search\"\r\n            placeholder={placeholder}\r\n            value={currentValue}\r\n            onChange={handleInputChange}\r\n            onKeyDown={handleKeyDown}\r\n            disabled={disabled || currentIsLoading}\r\n            className={cn(\r\n              \"pl-8\",\r\n              showClearButton && currentValue && \"pr-8\",\r\n              sizeClasses[size],\r\n              inputClassName\r\n            )}\r\n          />\r\n          {showClearButton && currentValue && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleClearClick}\r\n              disabled={disabled || currentIsLoading}\r\n              className=\"absolute right-2.5 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground disabled:opacity-50\"\r\n            >\r\n              <X className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Clear search</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {showSearchButton && (\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            onClick={handleSearchClick}\r\n            disabled={disabled || currentIsLoading}\r\n            className={cn(buttonSizeClasses[size], buttonClassName)}\r\n          >\r\n            {currentIsLoading ? (\r\n              <span className=\"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n            ) : (\r\n              <Search className=\"h-4 w-4\" />\r\n            )}\r\n            <span className=\"sr-only\">Search</span>\r\n          </Button>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nSearchInput.displayName = \"SearchInput\";\r\n\r\n/**\r\n * Simple search input for basic use cases\r\n */\r\nexport function SimpleSearchInput({\r\n  placeholder = \"Search...\",\r\n  onSearch,\r\n  className,\r\n  ...props\r\n}: {\r\n  placeholder?: string;\r\n  onSearch?: (query: string) => void;\r\n  className?: string;\r\n} & Omit<SearchInputProps, \"onSearch\">) {\r\n  return (\r\n    <SearchInput\r\n      placeholder={placeholder}\r\n      onSearch={onSearch}\r\n      mode=\"debounced\"\r\n      showClearButton={true}\r\n      className={cn(\"max-w-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\n/**\r\n * Manual search input with search button\r\n */\r\nexport function ManualSearchInput({\r\n  placeholder = \"Search...\",\r\n  onSearch,\r\n  className,\r\n  ...props\r\n}: {\r\n  placeholder?: string;\r\n  onSearch?: (query: string) => void;\r\n  className?: string;\r\n} & Omit<SearchInputProps, \"onSearch\" | \"mode\" | \"showSearchButton\">) {\r\n  return (\r\n    <SearchInput\r\n      placeholder={placeholder}\r\n      onSearch={onSearch}\r\n      mode=\"manual\"\r\n      showSearchButton={true}\r\n      showClearButton={true}\r\n      className={className}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\n/**\r\n * Real-time search input for instant filtering\r\n */\r\nexport function RealtimeSearchInput({\r\n  placeholder = \"Search...\",\r\n  onSearch,\r\n  className,\r\n  ...props\r\n}: {\r\n  placeholder?: string;\r\n  onSearch?: (query: string) => void;\r\n  className?: string;\r\n} & Omit<SearchInputProps, \"onSearch\" | \"mode\">) {\r\n  return (\r\n    <SearchInput\r\n      placeholder={placeholder}\r\n      onSearch={onSearch}\r\n      mode=\"realtime\"\r\n      showClearButton={true}\r\n      className={cn(\"max-w-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\n/**\r\n * Search input configurations for different use cases\r\n */\r\nexport const SearchConfigs = {\r\n  /**\r\n   * For large datasets that need server-side search\r\n   */\r\n  serverSide: {\r\n    mode: \"debounced\" as const,\r\n    debounceMs: 500,\r\n    minLength: 2,\r\n    showSearchButton: false,\r\n    showClearButton: true,\r\n  },\r\n\r\n  /**\r\n   * For client-side filtering of small datasets\r\n   */\r\n  clientSide: {\r\n    mode: \"realtime\" as const,\r\n    debounceMs: 100,\r\n    minLength: 0,\r\n    showSearchButton: false,\r\n    showClearButton: true,\r\n  },\r\n\r\n  /**\r\n   * For manual search with explicit search action\r\n   */\r\n  manual: {\r\n    mode: \"manual\" as const,\r\n    showSearchButton: true,\r\n    showClearButton: true,\r\n  },\r\n\r\n  /**\r\n   * For global search functionality\r\n   */\r\n  global: {\r\n    mode: \"debounced\" as const,\r\n    debounceMs: 300,\r\n    minLength: 1,\r\n    showSearchButton: false,\r\n    showClearButton: true,\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;;AA2EO,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,KAClC,CACE,EACE,cAAc,WAAW,EACzB,QAAQ,EAAE,EACV,QAAQ,EACR,OAAO,EACP,aAAa,GAAG,EAChB,YAAY,CAAC,EACb,mBAAmB,KAAK,EACxB,kBAAkB,IAAI,EACtB,OAAO,WAAW,EAClB,SAAS,EACT,cAAc,EACd,eAAe,EACf,YAAY,KAAK,EACjB,WAAW,KAAK,EAChB,OAAO,SAAS,EACjB,EACD;;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,0CAA0C;IAC1C,MAAM,EACJ,UAAU,EACV,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW,EACZ,GAAG,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD,EAAE;QACrB,cAAc;QACd,OAAO;QACP;QACA,UAAU,SAAS,cAAc,WAAW;QAC5C;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe,SAAS,WAAW,cAAc;IACvD,MAAM,mBAAmB,SAAS,cAAc,cAAc;IAE9D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAClC,CAAC;YACC,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;YAE/B,IAAI,SAAS,UAAU;gBACrB,eAAe;YACjB,OAAO,IAAI,SAAS,YAAY;gBAC9B,WAAW;YACb,OAAO;gBACL,aAAa;YACf;QACF;qDACA;QAAC;QAAM;QAAc;KAAS;IAGhC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACpC,IAAI,SAAS,UAAU;gBACrB,WAAW;YACb,OAAO;gBACL;YACF;QACF;qDAAG;QAAC;QAAM;QAAa;QAAU;KAAY;IAE7C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YACnC,IAAI,SAAS,UAAU;gBACrB,eAAe;gBACf;YACF,OAAO;gBACL;YACF;QACF;oDAAG;QAAC;QAAM;QAAa;KAAQ;IAE/B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAC9B,CAAC;YACC,IAAI,EAAE,GAAG,KAAK,SAAS;gBACrB,EAAE,cAAc;gBAChB;YACF;QACF;iDACA;QAAC;KAAkB;IAGrB,MAAM,cAAc;QAClB,IAAI;QACJ,SAAS;QACT,IAAI;IACN;IAEA,MAAM,oBAAoB;QACxB,IAAI;QACJ,SAAS;QACT,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAC5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC,oIAAA,CAAA,QAAK;wBACJ,KAAK;wBACL,MAAK;wBACL,aAAa;wBACb,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,UAAU,YAAY;wBACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,mBAAmB,gBAAgB,QACnC,WAAW,CAAC,KAAK,EACjB;;;;;;oBAGH,mBAAmB,8BAClB,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU,YAAY;wBACtB,WAAU;;0CAEV,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;YAK/B,kCACC,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,SAAS;gBACT,UAAU,YAAY;gBACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB,CAAC,KAAK,EAAE;;oBAEtC,iCACC,6LAAC;wBAAK,WAAU;;;;;6CAEhB,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAEpB,6LAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;;AAKpC;;QApHM,6IAAA,CAAA,qBAAkB;;;KA9Bb;AAqJb,YAAY,WAAW,GAAG;AAKnB,SAAS,kBAAkB,EAChC,cAAc,WAAW,EACzB,QAAQ,EACR,SAAS,EACT,GAAG,OAKiC;IACpC,qBACE,6LAAC;QACC,aAAa;QACb,UAAU;QACV,MAAK;QACL,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MApBgB;AAyBT,SAAS,kBAAkB,EAChC,cAAc,WAAW,EACzB,QAAQ,EACR,SAAS,EACT,GAAG,OAK+D;IAClE,qBACE,6LAAC;QACC,aAAa;QACb,UAAU;QACV,MAAK;QACL,kBAAkB;QAClB,iBAAiB;QACjB,WAAW;QACV,GAAG,KAAK;;;;;;AAGf;MArBgB;AA0BT,SAAS,oBAAoB,EAClC,cAAc,WAAW,EACzB,QAAQ,EACR,SAAS,EACT,GAAG,OAK0C;IAC7C,qBACE,6LAAC;QACC,aAAa;QACb,UAAU;QACV,MAAK;QACL,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MApBgB;AAyBT,MAAM,gBAAgB;IAC3B;;GAEC,GACD,YAAY;QACV,MAAM;QACN,YAAY;QACZ,WAAW;QACX,kBAAkB;QAClB,iBAAiB;IACnB;IAEA;;GAEC,GACD,YAAY;QACV,MAAM;QACN,YAAY;QACZ,WAAW;QACX,kBAAkB;QAClB,iBAAiB;IACnB;IAEA;;GAEC,GACD,QAAQ;QACN,MAAM;QACN,kBAAkB;QAClB,iBAAiB;IACnB;IAEA;;GAEC,GACD,QAAQ;QACN,MAAM;QACN,YAAY;QACZ,WAAW;QACX,kBAAkB;QAClB,iBAAiB;IACnB;AACF", "debugId": null}}, {"offset": {"line": 6412, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 6550, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/inventory/utils/export-inventory.ts"], "sourcesContent": ["import * as XLSX from \"xlsx\";\r\nimport { BranchInventory } from \"@/types/inventory\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\n\r\n/**\r\n * Export inventory data to Excel\r\n * @param inventory List of inventory items to export\r\n * @param filename Filename for the Excel file (without extension)\r\n */\r\nexport function exportInventoryToExcel(\r\n  inventory: BranchInventory[],\r\n  filename: string = \"inventory-export\"\r\n) {\r\n  // Define headers for the Excel file\r\n  const headers = [\r\n    \"Product Name\",\r\n    \"SKU\",\r\n    \"Barcode\",\r\n    \"Branch\",\r\n    \"Quantity\",\r\n    \"Reorder Level\",\r\n    \"Retail Price\",\r\n    \"Wholesale Price\",\r\n    \"Total Retail Value\",\r\n    \"Batch Number\",\r\n    \"Expiry Date\",\r\n    \"Last Updated\"\r\n  ];\r\n\r\n  // Map inventory data to rows\r\n  const rows = inventory.map(item => {\r\n    // Get product details from either Product or product property\r\n    const product = item.Product || item.product || {};\r\n    \r\n    // Get branch details from either Branch or branch property\r\n    const branch = item.Branch || item.branch || {};\r\n    \r\n    // Calculate prices\r\n    const retailPrice = parseFloat(item.default_selling_price) || parseFloat(item.selling_price) || 0;\r\n    const wholesalePrice = parseFloat(item.default_wholesale_price) || 0;\r\n    const totalRetailValue = retailPrice * (item.quantity || 0);\r\n    \r\n    // Format dates\r\n    const lastUpdated = item.updated_at || item.last_updated \r\n      ? new Date(item.updated_at || item.last_updated || \"\").toLocaleDateString()\r\n      : \"N/A\";\r\n    \r\n    const expiryDate = item.expiry_date\r\n      ? new Date(item.expiry_date).toLocaleDateString()\r\n      : \"N/A\";\r\n    \r\n    // Return row data\r\n    return [\r\n      product.name || \"Unknown Product\",\r\n      product.sku || \"N/A\",\r\n      product.barcode || \"N/A\",\r\n      branch.name || \"N/A\",\r\n      item.quantity || 0,\r\n      item.reorder_level || \"N/A\",\r\n      retailPrice,\r\n      wholesalePrice,\r\n      totalRetailValue,\r\n      item.batch_number || \"N/A\",\r\n      expiryDate,\r\n      lastUpdated\r\n    ];\r\n  });\r\n\r\n  // Create worksheet\r\n  const data = [headers, ...rows];\r\n  const ws = XLSX.utils.aoa_to_sheet(data);\r\n\r\n  // Set column widths\r\n  const colWidths = [\r\n    { wch: 30 },   // Product Name\r\n    { wch: 15 },   // SKU\r\n    { wch: 15 },   // Barcode\r\n    { wch: 20 },   // Branch\r\n    { wch: 10 },   // Quantity\r\n    { wch: 15 },   // Reorder Level\r\n    { wch: 15 },   // Retail Price\r\n    { wch: 15 },   // Wholesale Price\r\n    { wch: 15 },   // Total Retail Value\r\n    { wch: 15 },   // Batch Number\r\n    { wch: 15 },   // Expiry Date\r\n    { wch: 15 },   // Last Updated\r\n  ];\r\n  ws[\"!cols\"] = colWidths;\r\n\r\n  // Apply styles to header row\r\n  const headerStyle = {\r\n    font: { bold: true },\r\n    fill: { fgColor: { rgb: \"EFEFEF\" } },\r\n    alignment: { horizontal: \"center\", vertical: \"center\" }\r\n  };\r\n\r\n  // Apply styles to the first row (headers)\r\n  for (let i = 0; i < headers.length; i++) {\r\n    const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });\r\n    if (!ws[cellRef]) ws[cellRef] = {};\r\n    ws[cellRef].s = headerStyle;\r\n  }\r\n\r\n  // Create workbook\r\n  const wb = XLSX.utils.book_new();\r\n  XLSX.utils.book_append_sheet(wb, ws, \"Inventory\");\r\n\r\n  // Generate Excel file and trigger download\r\n  XLSX.writeFile(wb, `${filename}.xlsx`);\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AASO,SAAS,uBACd,SAA4B,EAC5B,WAAmB,kBAAkB;IAErC,oCAAoC;IACpC,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,6BAA6B;IAC7B,MAAM,OAAO,UAAU,GAAG,CAAC,CAAA;QACzB,8DAA8D;QAC9D,MAAM,UAAU,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,CAAC;QAEjD,2DAA2D;QAC3D,MAAM,SAAS,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,CAAC;QAE9C,mBAAmB;QACnB,MAAM,cAAc,WAAW,KAAK,qBAAqB,KAAK,WAAW,KAAK,aAAa,KAAK;QAChG,MAAM,iBAAiB,WAAW,KAAK,uBAAuB,KAAK;QACnE,MAAM,mBAAmB,cAAc,CAAC,KAAK,QAAQ,IAAI,CAAC;QAE1D,eAAe;QACf,MAAM,cAAc,KAAK,UAAU,IAAI,KAAK,YAAY,GACpD,IAAI,KAAK,KAAK,UAAU,IAAI,KAAK,YAAY,IAAI,IAAI,kBAAkB,KACvE;QAEJ,MAAM,aAAa,KAAK,WAAW,GAC/B,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,KAC7C;QAEJ,kBAAkB;QAClB,OAAO;YACL,QAAQ,IAAI,IAAI;YAChB,QAAQ,GAAG,IAAI;YACf,QAAQ,OAAO,IAAI;YACnB,OAAO,IAAI,IAAI;YACf,KAAK,QAAQ,IAAI;YACjB,KAAK,aAAa,IAAI;YACtB;YACA;YACA;YACA,KAAK,YAAY,IAAI;YACrB;YACA;SACD;IACH;IAEA,mBAAmB;IACnB,MAAM,OAAO;QAAC;WAAY;KAAK;IAC/B,MAAM,KAAK,gIAAA,CAAA,QAAU,CAAC,YAAY,CAAC;IAEnC,oBAAoB;IACpB,MAAM,YAAY;QAChB;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;KACX;IACD,EAAE,CAAC,QAAQ,GAAG;IAEd,6BAA6B;IAC7B,MAAM,cAAc;QAClB,MAAM;YAAE,MAAM;QAAK;QACnB,MAAM;YAAE,SAAS;gBAAE,KAAK;YAAS;QAAE;QACnC,WAAW;YAAE,YAAY;YAAU,UAAU;QAAS;IACxD;IAEA,0CAA0C;IAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,MAAM,UAAU,gIAAA,CAAA,QAAU,CAAC,WAAW,CAAC;YAAE,GAAG;YAAG,GAAG;QAAE;QACpD,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,GAAG,CAAC;QACjC,EAAE,CAAC,QAAQ,CAAC,CAAC,GAAG;IAClB;IAEA,kBAAkB;IAClB,MAAM,KAAK,gIAAA,CAAA,QAAU,CAAC,QAAQ;IAC9B,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,IAAI,IAAI;IAErC,2CAA2C;IAC3C,CAAA,GAAA,gIAAA,CAAA,YAAc,AAAD,EAAE,IAAI,GAAG,SAAS,KAAK,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 6685, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/inventory/components/inventory-table.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { DataPagination } from \"@/components/ui/data-pagination\";\r\nimport { ManualSearchInput } from \"@/components/ui/search-input\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { BranchInventory } from \"@/types/inventory\";\r\nimport {\r\n  AlertTriangle,\r\n  ArrowUpDown,\r\n  Download,\r\n  FileSpreadsheet,\r\n  Package,\r\n  Plus,\r\n  Upload,\r\n} from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { exportInventoryToExcel } from \"../utils/export-inventory\";\r\n\r\ninterface InventoryTableProps {\r\n  inventory: BranchInventory[];\r\n  isLoading: boolean;\r\n  onSearch?: (query: string) => void;\r\n  pagination?: {\r\n    page: number;\r\n    totalPages: number;\r\n    onPageChange: (page: number) => void;\r\n    limit?: number;\r\n    onItemsPerPageChange?: (value: string) => void;\r\n    total?: number;\r\n  };\r\n}\r\n\r\nexport function InventoryTable({\r\n  inventory,\r\n  isLoading,\r\n  onSearch,\r\n  pagination,\r\n}: InventoryTableProps) {\r\n  const router = useRouter();\r\n  // Search is now handled by ManualSearchInput component\r\n\r\n  const handleExportToExcel = () => {\r\n    if (inventory && inventory.length > 0) {\r\n      exportInventoryToExcel(\r\n        inventory,\r\n        `inventory-export-${new Date().toISOString().split(\"T\")[0]}`\r\n      );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Search and action buttons */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between gap-4\">\r\n        <ManualSearchInput\r\n          placeholder=\"Search inventory by product name...\"\r\n          onSearch={onSearch}\r\n          className=\"w-full max-w-sm\"\r\n        />\r\n        <div className=\"flex flex-wrap gap-2\">\r\n          <Button onClick={() => router.push(\"/inventory/add\")}>\r\n            <Plus className=\"mr-2 h-4 w-4\" /> Add Stock Item\r\n          </Button>\r\n          <Button onClick={() => router.push(\"/inventory/import\")}>\r\n            <Upload className=\"mr-2 h-4 w-4\" /> Import Stock Items\r\n          </Button>\r\n          <Button onClick={() => router.push(\"/inventory/adjust\")}>\r\n            <Plus className=\"mr-2 h-4 w-4\" /> Adjust Stock\r\n          </Button>\r\n          <Button onClick={() => router.push(\"/inventory/excel\")}>\r\n            <FileSpreadsheet className=\"mr-2 h-4 w-4\" /> Excel Import/Export\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={handleExportToExcel}\r\n            disabled={isLoading || inventory.length === 0}\r\n          >\r\n            <Download className=\"mr-2 h-4 w-4\" /> Export to Excel\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Inventory table */}\r\n      <div className=\"rounded-md border\">\r\n        <Table>\r\n          <TableHeader>\r\n            <TableRow>\r\n              <TableHead>Product</TableHead>\r\n              <TableHead>\r\n                <div className=\"flex items-center\">\r\n                  Stock Quantity\r\n                  <ArrowUpDown className=\"ml-2 h-4 w-4\" />\r\n                </div>\r\n              </TableHead>\r\n              <TableHead>Reorder Level</TableHead>\r\n              <TableHead>Retail Price</TableHead>\r\n              <TableHead>Wholesale Price</TableHead>\r\n              <TableHead>Total Retail Value</TableHead>\r\n              <TableHead>Branch</TableHead>\r\n              <TableHead>Last Updated</TableHead>\r\n              <TableHead className=\"text-right\">Actions</TableHead>\r\n            </TableRow>\r\n          </TableHeader>\r\n          <TableBody>\r\n            {isLoading ? (\r\n              // Show loading skeletons when loading\r\n              Array.from({ length: 5 }).map((_, index) => (\r\n                <TableRow key={index}>\r\n                  <TableCell>\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <Skeleton className=\"h-10 w-10 rounded-full\" />\r\n                      <div className=\"space-y-1\">\r\n                        <Skeleton className=\"h-4 w-24\" />\r\n                        <Skeleton className=\"h-3 w-16\" />\r\n                      </div>\r\n                    </div>\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-10\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-10\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-16\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-16\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-16\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-16\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-24\" />\r\n                  </TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    <Skeleton className=\"h-9 w-16 ml-auto\" />\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))\r\n            ) : inventory.length === 0 ? (\r\n              // Show message when no items are found\r\n              <TableRow>\r\n                <TableCell colSpan={9} className=\"h-32 text-center\">\r\n                  <div className=\"flex flex-col items-center justify-center space-y-2\">\r\n                    <Package className=\"h-8 w-8 text-muted-foreground\" />\r\n                    <div className=\"text-sm font-medium\">\r\n                      No inventory items found\r\n                    </div>\r\n                    <div className=\"text-xs text-muted-foreground\">\r\n                      Try adjusting your search or add new products to inventory\r\n                    </div>\r\n                  </div>\r\n                </TableCell>\r\n              </TableRow>\r\n            ) : (\r\n              // Show inventory items\r\n              inventory.map((item) => {\r\n                // Safely handle potentially undefined values\r\n                const isLowStock =\r\n                  item.reorder_level && item.quantity <= item.reorder_level;\r\n\r\n                // Get prices from different possible locations\r\n                const retailPrice =\r\n                  (item.default_selling_price\r\n                    ? parseFloat(item.default_selling_price)\r\n                    : 0) ||\r\n                  (item.selling_price ? parseFloat(item.selling_price) : 0) ||\r\n                  0;\r\n\r\n                const wholesalePrice =\r\n                  (item.default_wholesale_price\r\n                    ? parseFloat(item.default_wholesale_price)\r\n                    : 0) || 0;\r\n\r\n                // Calculate total retail value\r\n                const totalRetailValue = retailPrice * (item.quantity || 0);\r\n\r\n                return (\r\n                  <TableRow key={item.id}>\r\n                    <TableCell>\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <Avatar className=\"h-10 w-10\">\r\n                          <AvatarImage\r\n                            src={\r\n                              item.Product?.image_url || item.product?.image_url\r\n                            }\r\n                            alt={item.Product?.name || item.product?.name}\r\n                          />\r\n                          <AvatarFallback>\r\n                            {item.Product?.name || item.product?.name\r\n                              ? (\r\n                                  (item.Product?.name ||\r\n                                    item.product?.name) as string\r\n                                )\r\n                                  .split(\" \")\r\n                                  .map((n) => n[0] || \"\")\r\n                                  .join(\"\")\r\n                                  .toUpperCase()\r\n                                  .substring(0, 2)\r\n                              : \"NA\"}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <div className=\"font-medium\">\r\n                            {item.Product?.name ||\r\n                              item.product?.name ||\r\n                              \"Unknown Product\"}\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">\r\n                            SKU:{\" \"}\r\n                            {item.Product?.sku || item.product?.sku || \"N/A\"}\r\n                          </div>\r\n                          {(item.Product?.barcode || item.product?.barcode) && (\r\n                            <div className=\"text-sm text-muted-foreground\">\r\n                              Barcode:{\" \"}\r\n                              {item.Product?.barcode || item.product?.barcode}\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    </TableCell>\r\n                    <TableCell>\r\n                      {isLowStock ? (\r\n                        <Badge\r\n                          variant=\"destructive\"\r\n                          className=\"flex items-center gap-1\"\r\n                        >\r\n                          <AlertTriangle className=\"h-3 w-3\" />\r\n                          {item.quantity}\r\n                        </Badge>\r\n                      ) : (\r\n                        item.quantity\r\n                      )}\r\n                    </TableCell>\r\n                    <TableCell>{item.reorder_level || \"N/A\"}</TableCell>\r\n                    <TableCell>{formatCurrency(retailPrice)}</TableCell>\r\n                    <TableCell>{formatCurrency(wholesalePrice)}</TableCell>\r\n                    <TableCell>{formatCurrency(totalRetailValue)}</TableCell>\r\n                    <TableCell>\r\n                      {item.Branch?.name || item.branch?.name || \"N/A\"}\r\n                    </TableCell>\r\n                    <TableCell>\r\n                      {item.updated_at || item.last_updated\r\n                        ? new Date(\r\n                            item.updated_at || item.last_updated || \"\"\r\n                          ).toLocaleDateString()\r\n                        : \"N/A\"}\r\n                    </TableCell>\r\n                    <TableCell className=\"text-right\">\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() =>\r\n                          router.push(`/inventory/stock-items/${item.id}`)\r\n                        }\r\n                      >\r\n                        View\r\n                      </Button>\r\n                    </TableCell>\r\n                  </TableRow>\r\n                );\r\n              })\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n\r\n      {/* Pagination controls */}\r\n      {pagination && (\r\n        <div className=\"px-4 py-2 border-t\">\r\n          <DataPagination\r\n            currentPage={pagination.page}\r\n            totalPages={pagination.totalPages}\r\n            onPageChange={pagination.onPageChange}\r\n            pageSize={pagination.limit || 10}\r\n            onPageSizeChange={(newPageSize) => {\r\n              if (pagination.onItemsPerPageChange) {\r\n                pagination.onItemsPerPageChange(newPageSize.toString());\r\n              }\r\n            }}\r\n            totalItems={pagination.total || 0}\r\n            isLoading={isLoading}\r\n            showPageSizeSelector={true}\r\n            showItemsInfo={true}\r\n            showFirstLastButtons={true}\r\n          />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AA5BA;;;;;;;;;;;;AA4CO,SAAS,eAAe,EAC7B,SAAS,EACT,SAAS,EACT,QAAQ,EACR,UAAU,EACU;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,uDAAuD;IAEvD,MAAM,sBAAsB;QAC1B,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;YACrC,CAAA,GAAA,+JAAA,CAAA,yBAAsB,AAAD,EACnB,WACA,CAAC,iBAAiB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;QAEhE;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8IAAA,CAAA,oBAAiB;wBAChB,aAAY;wBACZ,UAAU;wBACV,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;kDACjC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAEnC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;kDACjC,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAErC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;kDACjC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAEnC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;kDACjC,6LAAC,+NAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAE9C,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,aAAa,UAAU,MAAM,KAAK;;kDAE5C,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;sCACJ,6LAAC,oIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;kDACP,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDACR,cAAA,6LAAC;4CAAI,WAAU;;gDAAoB;8DAEjC,6LAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG3B,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAa;;;;;;;;;;;;;;;;;sCAGtC,6LAAC,oIAAA,CAAA,YAAS;sCACP,YACC,sCAAsC;4BACtC,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC,oIAAA,CAAA,WAAQ;;sDACP,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC,uIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAI1B,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;mCAhCT;;;;4CAoCf,UAAU,MAAM,KAAK,IACvB,uCAAuC;0CACvC,6LAAC,oIAAA,CAAA,WAAQ;0CACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oCAAC,SAAS;oCAAG,WAAU;8CAC/B,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAI,WAAU;0DAAsB;;;;;;0DAGrC,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;uCAOrD,uBAAuB;4BACvB,UAAU,GAAG,CAAC,CAAC;gCACb,6CAA6C;gCAC7C,MAAM,aACJ,KAAK,aAAa,IAAI,KAAK,QAAQ,IAAI,KAAK,aAAa;gCAE3D,+CAA+C;gCAC/C,MAAM,cACJ,CAAC,KAAK,qBAAqB,GACvB,WAAW,KAAK,qBAAqB,IACrC,CAAC,KACL,CAAC,KAAK,aAAa,GAAG,WAAW,KAAK,aAAa,IAAI,CAAC,KACxD;gCAEF,MAAM,iBACJ,CAAC,KAAK,uBAAuB,GACzB,WAAW,KAAK,uBAAuB,IACvC,CAAC,KAAK;gCAEZ,+BAA+B;gCAC/B,MAAM,mBAAmB,cAAc,CAAC,KAAK,QAAQ,IAAI,CAAC;gCAE1D,qBACE,6LAAC,oIAAA,CAAA,WAAQ;;sDACP,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,6LAAC,qIAAA,CAAA,cAAW;gEACV,KACE,KAAK,OAAO,EAAE,aAAa,KAAK,OAAO,EAAE;gEAE3C,KAAK,KAAK,OAAO,EAAE,QAAQ,KAAK,OAAO,EAAE;;;;;;0EAE3C,6LAAC,qIAAA,CAAA,iBAAc;0EACZ,KAAK,OAAO,EAAE,QAAQ,KAAK,OAAO,EAAE,OACjC,AACE,CAAC,KAAK,OAAO,EAAE,QACb,KAAK,OAAO,EAAE,IAAI,EAEnB,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,IAAI,IACnB,IAAI,CAAC,IACL,WAAW,GACX,SAAS,CAAC,GAAG,KAChB;;;;;;;;;;;;kEAGR,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EACZ,KAAK,OAAO,EAAE,QACb,KAAK,OAAO,EAAE,QACd;;;;;;0EAEJ,6LAAC;gEAAI,WAAU;;oEAAgC;oEACxC;oEACJ,KAAK,OAAO,EAAE,OAAO,KAAK,OAAO,EAAE,OAAO;;;;;;;4DAE5C,CAAC,KAAK,OAAO,EAAE,WAAW,KAAK,OAAO,EAAE,OAAO,mBAC9C,6LAAC;gEAAI,WAAU;;oEAAgC;oEACpC;oEACR,KAAK,OAAO,EAAE,WAAW,KAAK,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;sDAMlD,6LAAC,oIAAA,CAAA,YAAS;sDACP,2BACC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;;kEAEV,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDACxB,KAAK,QAAQ;;;;;;uDAGhB,KAAK,QAAQ;;;;;;sDAGjB,6LAAC,oIAAA,CAAA,YAAS;sDAAE,KAAK,aAAa,IAAI;;;;;;sDAClC,6LAAC,oIAAA,CAAA,YAAS;sDAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;sDAC3B,6LAAC,oIAAA,CAAA,YAAS;sDAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;sDAC3B,6LAAC,oIAAA,CAAA,YAAS;sDAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;sDAC3B,6LAAC,oIAAA,CAAA,YAAS;sDACP,KAAK,MAAM,EAAE,QAAQ,KAAK,MAAM,EAAE,QAAQ;;;;;;sDAE7C,6LAAC,oIAAA,CAAA,YAAS;sDACP,KAAK,UAAU,IAAI,KAAK,YAAY,GACjC,IAAI,KACF,KAAK,UAAU,IAAI,KAAK,YAAY,IAAI,IACxC,kBAAkB,KACpB;;;;;;sDAEN,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IACP,OAAO,IAAI,CAAC,CAAC,uBAAuB,EAAE,KAAK,EAAE,EAAE;0DAElD;;;;;;;;;;;;mCA7EU,KAAK,EAAE;;;;;4BAmF1B;;;;;;;;;;;;;;;;;YAOP,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,iBAAc;oBACb,aAAa,WAAW,IAAI;oBAC5B,YAAY,WAAW,UAAU;oBACjC,cAAc,WAAW,YAAY;oBACrC,UAAU,WAAW,KAAK,IAAI;oBAC9B,kBAAkB,CAAC;wBACjB,IAAI,WAAW,oBAAoB,EAAE;4BACnC,WAAW,oBAAoB,CAAC,YAAY,QAAQ;wBACtD;oBACF;oBACA,YAAY,WAAW,KAAK,IAAI;oBAChC,WAAW;oBACX,sBAAsB;oBACtB,eAAe;oBACf,sBAAsB;;;;;;;;;;;;;;;;;AAMlC;GAtQgB;;QAMC,qIAAA,CAAA,YAAS;;;KANV", "debugId": null}}, {"offset": {"line": 7398, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 7513, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/dsa/api/dsa-stock-assignment-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport {\r\n  CreateDsaStockAssignmentRequest,\r\n  DsaStockAssignment,\r\n  ReturnStockRequest,\r\n  UpdateDsaStockAssignmentRequest,\r\n  ReconcileBatchRequest,\r\n} from \"@/types/dsa\";\r\nimport { AxiosError } from \"axios\";\r\n\r\n/**\r\n * DSA Stock Assignment Service\r\n */\r\nexport const DsaStockAssignmentService = {\r\n  /**\r\n   * Get all DSA stock assignments\r\n   */\r\n  getAssignments: async (params?: {\r\n    dsa_agent_id?: number;\r\n    branch_id?: number;\r\n    product_id?: number;\r\n  }): Promise<DsaStockAssignment[]> => {\r\n    try {\r\n      console.log('DsaStockAssignmentService - getAssignments - params:', params);\r\n\r\n      // Map dsa_agent_id to customer_id for backward compatibility\r\n      const apiParams = { ...params };\r\n      if (params?.dsa_agent_id) {\r\n        apiParams.customer_id = params.dsa_agent_id;\r\n        delete apiParams.dsa_agent_id;\r\n      }\r\n\r\n      const response = await apiClient.get<any>(\r\n        \"/dsa-stock-assignments\",\r\n        { params: apiParams }\r\n      );\r\n\r\n      console.log('DsaStockAssignmentService - getAssignments - response:', response);\r\n\r\n      // Handle different response formats\r\n      if (response && typeof response === 'object') {\r\n        if (Array.isArray(response.data)) {\r\n          // If response.data is an array, use that\r\n          return response.data;\r\n        } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\r\n          // If response.data.data is an array, use that\r\n          return response.data.data;\r\n        } else if (Array.isArray(response)) {\r\n          // If response is already an array, use it directly\r\n          return response;\r\n        }\r\n      }\r\n\r\n      // Default return empty array if response format is unexpected\r\n      console.warn('DsaStockAssignmentService - getAssignments - unexpected response format:', response);\r\n      return [];\r\n    } catch (error) {\r\n      console.error('Error fetching DSA stock assignments:', error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      return [];\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a DSA stock assignment by ID\r\n   */\r\n  getAssignmentById: async (id: number): Promise<DsaStockAssignment> => {\r\n    try {\r\n      console.log(`DsaStockAssignmentService - getAssignmentById - id: ${id}`);\r\n\r\n      const response = await apiClient.get<any>(\r\n        `/dsa-stock-assignments/${id}`\r\n      );\r\n\r\n      console.log('DsaStockAssignmentService - getAssignmentById - response:', response);\r\n\r\n      // Handle different response formats\r\n      if (response && typeof response === 'object') {\r\n        if (response.data) {\r\n          // If response has a data property, use that\r\n          return response.data;\r\n        } else {\r\n          // Otherwise use the response directly\r\n          return response;\r\n        }\r\n      }\r\n\r\n      throw new Error('Invalid response format');\r\n    } catch (error) {\r\n      console.error(`Error fetching DSA stock assignment with ID ${id}:`, error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get unreconciled assignments for a DSA agent\r\n   */\r\n  getUnreconciledAssignments: async (dsa_agent_id: number): Promise<DsaStockAssignment[]> => {\r\n    try {\r\n      console.log(`DsaStockAssignmentService - getUnreconciledAssignments - dsa_agent_id: ${dsa_agent_id}`);\r\n\r\n      // Map dsa_agent_id to customer_id for backward compatibility\r\n      const customer_id = dsa_agent_id;\r\n\r\n      const response = await apiClient.get<any>(\r\n        \"/dsa-stock-assignments/unreconciled\",\r\n        { params: { customer_id } }\r\n      );\r\n\r\n      console.log('DsaStockAssignmentService - getUnreconciledAssignments - response:', response);\r\n\r\n      // Handle different response formats\r\n      if (response && typeof response === 'object') {\r\n        if (response.data && response.data.data && Array.isArray(response.data.data)) {\r\n          // If response.data.data is an array, use that\r\n          return response.data.data;\r\n        } else if (Array.isArray(response.data)) {\r\n          // If response.data is an array, use that\r\n          return response.data;\r\n        } else if (Array.isArray(response)) {\r\n          // If response is already an array, use it directly\r\n          return response;\r\n        }\r\n      }\r\n\r\n      // Default return empty array if response format is unexpected\r\n      console.warn('DsaStockAssignmentService - getUnreconciledAssignments - unexpected response format:', response);\r\n      return [];\r\n    } catch (error) {\r\n      console.error(`Error fetching unreconciled assignments for DSA agent ${dsa_agent_id}:`, error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      return [];\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get assignments grouped by assignment_identifier\r\n   */\r\n  getAssignmentsByIdentifier: async (params?: {\r\n    branch_id?: number;\r\n  }): Promise<any[]> => {\r\n    try {\r\n      console.log('DsaStockAssignmentService - getAssignmentsByIdentifier - params:', params);\r\n\r\n      const response = await apiClient.get<any>(\r\n        \"/dsa-stock-assignments/by-identifier\",\r\n        { params }\r\n      );\r\n\r\n      console.log('DsaStockAssignmentService - getAssignmentsByIdentifier - response:', response);\r\n\r\n      // Handle different response formats\r\n      if (response && typeof response === 'object') {\r\n        if (response.data && response.data.data && Array.isArray(response.data.data)) {\r\n          // If response.data.data is an array, use that\r\n          return response.data.data;\r\n        } else if (Array.isArray(response.data)) {\r\n          // If response.data is an array, use that\r\n          return response.data;\r\n        } else if (Array.isArray(response)) {\r\n          // If response is already an array, use it directly\r\n          return response;\r\n        }\r\n      }\r\n\r\n      // Default return empty array if response format is unexpected\r\n      console.warn('DsaStockAssignmentService - getAssignmentsByIdentifier - unexpected response format:', response);\r\n      return [];\r\n    } catch (error) {\r\n      console.error('Error fetching assignments by identifier:', error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      return [];\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new DSA stock assignment with multiple items\r\n   */\r\n  createAssignment: async (\r\n    data: CreateDsaStockAssignmentRequest\r\n  ): Promise<DsaStockAssignment[]> => {\r\n    try {\r\n      console.log('DsaStockAssignmentService - createAssignment - request data:', data);\r\n\r\n      // Ensure we're sending the required fields to the API\r\n      const apiRequest = {\r\n        customer_id: data.dsa_agent_id, // Map dsa_agent_id to customer_id for backward compatibility\r\n        branch_id: data.branch_id,\r\n        items: data.items,\r\n        assignment_identifier: data.assignment_identifier,\r\n      };\r\n\r\n      const response = await apiClient.post<any>(\r\n        \"/dsa-stock-assignments/batch\",\r\n        apiRequest\r\n      );\r\n\r\n      console.log('DsaStockAssignmentService - createAssignment - response:', response);\r\n\r\n      // Handle different response formats\r\n      if (response && typeof response === 'object') {\r\n        if (response.data && response.data.assignments) {\r\n          // If response has a data.assignments property, use that\r\n          return response.data.assignments;\r\n        } else if (Array.isArray(response.data)) {\r\n          // If response.data is an array, use that\r\n          return response.data;\r\n        } else if (Array.isArray(response)) {\r\n          // If response is already an array, use it directly\r\n          return response;\r\n        } else {\r\n          // If it's a single object, wrap it in an array\r\n          return [response];\r\n        }\r\n      }\r\n\r\n      // Default return empty array if response format is unexpected\r\n      console.warn('DsaStockAssignmentService - createAssignment - unexpected response format:', response);\r\n      return [];\r\n    } catch (error) {\r\n      console.error('Error creating DSA stock assignment:', error);\r\n      const axiosError = error as AxiosError;\r\n\r\n      // Check for transaction rollback error\r\n      if (error instanceof Error && error.message && error.message.includes('Transaction cannot be rolled back')) {\r\n        console.error('Transaction rollback error detected. This may be due to a database constraint violation.');\r\n        throw new Error('Unable to create assignment. Please check if the DSA agent has reached their stock limit or if there are any duplicate assignments.');\r\n      }\r\n\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update a DSA stock assignment\r\n   */\r\n  updateAssignment: async (\r\n    id: number,\r\n    data: UpdateDsaStockAssignmentRequest\r\n  ): Promise<DsaStockAssignment> => {\r\n    try {\r\n      console.log(`DsaStockAssignmentService - updateAssignment - id: ${id}, data:`, data);\r\n\r\n      const response = await apiClient.put<any>(\r\n        `/dsa-stock-assignments/${id}`,\r\n        data\r\n      );\r\n\r\n      console.log('DsaStockAssignmentService - updateAssignment - response:', response);\r\n\r\n      // Handle different response formats\r\n      if (response && typeof response === 'object') {\r\n        if (response.data) {\r\n          // If response has a data property, use that\r\n          return response.data;\r\n        } else {\r\n          // Otherwise use the response directly\r\n          return response;\r\n        }\r\n      }\r\n\r\n      throw new Error('Invalid response format');\r\n    } catch (error) {\r\n      console.error(`Error updating DSA stock assignment with ID ${id}:`, error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete a DSA stock assignment\r\n   */\r\n  deleteAssignment: async (id: number): Promise<void> => {\r\n    try {\r\n      console.log(`DsaStockAssignmentService - deleteAssignment - id: ${id}`);\r\n\r\n      await apiClient.delete(`/dsa-stock-assignments/${id}`);\r\n\r\n      console.log(`DsaStockAssignmentService - deleteAssignment - successfully deleted assignment with ID ${id}`);\r\n    } catch (error) {\r\n      console.error(`Error deleting DSA stock assignment with ID ${id}:`, error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Record stock return from DSA agent\r\n   */\r\n  returnStock: async (\r\n    id: number,\r\n    data: ReturnStockRequest\r\n  ): Promise<DsaStockAssignment> => {\r\n    try {\r\n      console.log(`DsaStockAssignmentService - returnStock - id: ${id}, data:`, data);\r\n\r\n      const response = await apiClient.post<any>(\r\n        `/dsa-stock-assignments/${id}/return`,\r\n        data\r\n      );\r\n\r\n      console.log('DsaStockAssignmentService - returnStock - response:', response);\r\n\r\n      // Handle different response formats\r\n      if (response && typeof response === 'object') {\r\n        if (response.data) {\r\n          // If response has a data property, use that\r\n          return response.data;\r\n        } else {\r\n          // Otherwise use the response directly\r\n          return response;\r\n        }\r\n      }\r\n\r\n      throw new Error('Invalid response format');\r\n    } catch (error) {\r\n      console.error(`Error recording stock return for assignment with ID ${id}:`, error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Reconcile multiple DSA stock assignments in a batch\r\n   */\r\n  reconcileBatch: async (\r\n    data: ReconcileBatchRequest\r\n  ): Promise<any> => {\r\n    try {\r\n      console.log('DsaStockAssignmentService - reconcileBatch - data:', data);\r\n\r\n      // Map dsa_agent_id to customer_id for backward compatibility\r\n      const apiRequest = {\r\n        ...data,\r\n        customer_id: data.dsa_agent_id\r\n      };\r\n\r\n      const response = await apiClient.post<any>(\r\n        \"/dsa-stock-assignments/reconcile\",\r\n        apiRequest\r\n      );\r\n\r\n      console.log('DsaStockAssignmentService - reconcileBatch - response:', response);\r\n\r\n      // Handle different response formats\r\n      if (response && typeof response === 'object') {\r\n        if (response.data && response.data.data) {\r\n          // If response has a data.data property, use that\r\n          return response.data.data;\r\n        } else if (response.data) {\r\n          // If response has a data property, use that\r\n          return response.data;\r\n        } else {\r\n          // Otherwise use the response directly\r\n          return response;\r\n        }\r\n      }\r\n\r\n      throw new Error('Invalid response format');\r\n    } catch (error) {\r\n      console.error('Error reconciling DSA stock assignments:', error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get reconciliation details for an assignment identifier\r\n   */\r\n  getReconciliationByIdentifier: async (\r\n    identifier: string\r\n  ): Promise<any> => {\r\n    try {\r\n      console.log(`DsaStockAssignmentService - getReconciliationByIdentifier - identifier: ${identifier}`);\r\n\r\n      // First, get the assignments for this identifier to find the reconciliation ID\r\n      const assignments = await DsaStockAssignmentService.getAssignmentsByIdentifier();\r\n\r\n      const assignmentGroup = assignments.find(\r\n        (group) => group.assignment_identifier === identifier\r\n      );\r\n\r\n      if (!assignmentGroup || !assignmentGroup.items || assignmentGroup.items.length === 0) {\r\n        throw new Error('Assignment not found');\r\n      }\r\n\r\n      // Check if any of the items have a reconciliation_id\r\n      const reconciliationId = assignmentGroup.items.find(item => item.reconciliation_id)?.reconciliation_id;\r\n\r\n      if (!reconciliationId) {\r\n        // If no reconciliation found, return null\r\n        return null;\r\n      }\r\n\r\n      // Fetch the reconciliation details\r\n      const response = await apiClient.get<any>(\r\n        `/dsa-stock-reconciliations/${reconciliationId}`\r\n      );\r\n\r\n      console.log('DsaStockAssignmentService - getReconciliationByIdentifier - response:', response);\r\n\r\n      // Handle different response formats\r\n      if (response && typeof response === 'object') {\r\n        if (response.data) {\r\n          // If response has a data property, use that\r\n          return response.data;\r\n        } else {\r\n          // Otherwise use the response directly\r\n          return response;\r\n        }\r\n      }\r\n\r\n      throw new Error('Invalid response format');\r\n    } catch (error) {\r\n      console.error(`Error fetching reconciliation for identifier ${identifier}:`, error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      return null;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAaO,MAAM,4BAA4B;IACvC;;GAEC,GACD,gBAAgB,OAAO;QAKrB,IAAI;YACF,QAAQ,GAAG,CAAC,wDAAwD;YAEpE,6DAA6D;YAC7D,MAAM,YAAY;gBAAE,GAAG,MAAM;YAAC;YAC9B,IAAI,QAAQ,cAAc;gBACxB,UAAU,WAAW,GAAG,OAAO,YAAY;gBAC3C,OAAO,UAAU,YAAY;YAC/B;YAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,0BACA;gBAAE,QAAQ;YAAU;YAGtB,QAAQ,GAAG,CAAC,0DAA0D;YAEtE,oCAAoC;YACpC,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBAChC,yCAAyC;oBACzC,OAAO,SAAS,IAAI;gBACtB,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBACnF,8CAA8C;oBAC9C,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO,IAAI,MAAM,OAAO,CAAC,WAAW;oBAClC,mDAAmD;oBACnD,OAAO;gBACT;YACF;YAEA,8DAA8D;YAC9D,QAAQ,IAAI,CAAC,4EAA4E;YACzF,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,mBAAmB,OAAO;QACxB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,IAAI;YAEvE,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,uBAAuB,EAAE,IAAI;YAGhC,QAAQ,GAAG,CAAC,6DAA6D;YAEzE,oCAAoC;YACpC,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,SAAS,IAAI,EAAE;oBACjB,4CAA4C;oBAC5C,OAAO,SAAS,IAAI;gBACtB,OAAO;oBACL,sCAAsC;oBACtC,OAAO;gBACT;YACF;YAEA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,GAAG,CAAC,CAAC,EAAE;YACpE,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,MAAM;QACR;IACF;IAEA;;GAEC,GACD,4BAA4B,OAAO;QACjC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,uEAAuE,EAAE,cAAc;YAEpG,6DAA6D;YAC7D,MAAM,cAAc;YAEpB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,uCACA;gBAAE,QAAQ;oBAAE;gBAAY;YAAE;YAG5B,QAAQ,GAAG,CAAC,sEAAsE;YAElF,oCAAoC;YACpC,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBAC5E,8CAA8C;oBAC9C,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACvC,yCAAyC;oBACzC,OAAO,SAAS,IAAI;gBACtB,OAAO,IAAI,MAAM,OAAO,CAAC,WAAW;oBAClC,mDAAmD;oBACnD,OAAO;gBACT;YACF;YAEA,8DAA8D;YAC9D,QAAQ,IAAI,CAAC,wFAAwF;YACrG,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,aAAa,CAAC,CAAC,EAAE;YACxF,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,4BAA4B,OAAO;QAGjC,IAAI;YACF,QAAQ,GAAG,CAAC,oEAAoE;YAEhF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,wCACA;gBAAE;YAAO;YAGX,QAAQ,GAAG,CAAC,sEAAsE;YAElF,oCAAoC;YACpC,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBAC5E,8CAA8C;oBAC9C,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACvC,yCAAyC;oBACzC,OAAO,SAAS,IAAI;gBACtB,OAAO,IAAI,MAAM,OAAO,CAAC,WAAW;oBAClC,mDAAmD;oBACnD,OAAO;gBACT;YACF;YAEA,8DAA8D;YAC9D,QAAQ,IAAI,CAAC,wFAAwF;YACrG,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,kBAAkB,OAChB;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,gEAAgE;YAE5E,sDAAsD;YACtD,MAAM,aAAa;gBACjB,aAAa,KAAK,YAAY;gBAC9B,WAAW,KAAK,SAAS;gBACzB,OAAO,KAAK,KAAK;gBACjB,uBAAuB,KAAK,qBAAqB;YACnD;YAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CACnC,gCACA;YAGF,QAAQ,GAAG,CAAC,4DAA4D;YAExE,oCAAoC;YACpC,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,WAAW,EAAE;oBAC9C,wDAAwD;oBACxD,OAAO,SAAS,IAAI,CAAC,WAAW;gBAClC,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACvC,yCAAyC;oBACzC,OAAO,SAAS,IAAI;gBACtB,OAAO,IAAI,MAAM,OAAO,CAAC,WAAW;oBAClC,mDAAmD;oBACnD,OAAO;gBACT,OAAO;oBACL,+CAA+C;oBAC/C,OAAO;wBAAC;qBAAS;gBACnB;YACF;YAEA,8DAA8D;YAC9D,QAAQ,IAAI,CAAC,8EAA8E;YAC3F,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,aAAa;YAEnB,uCAAuC;YACvC,IAAI,iBAAiB,SAAS,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sCAAsC;gBAC1G,QAAQ,KAAK,CAAC;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,MAAM;QACR;IACF;IAEA;;GAEC,GACD,kBAAkB,OAChB,IACA;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,GAAG,OAAO,CAAC,EAAE;YAE/E,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,uBAAuB,EAAE,IAAI,EAC9B;YAGF,QAAQ,GAAG,CAAC,4DAA4D;YAExE,oCAAoC;YACpC,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,SAAS,IAAI,EAAE;oBACjB,4CAA4C;oBAC5C,OAAO,SAAS,IAAI;gBACtB,OAAO;oBACL,sCAAsC;oBACtC,OAAO;gBACT;YACF;YAEA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,GAAG,CAAC,CAAC,EAAE;YACpE,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,MAAM;QACR;IACF;IAEA;;GAEC,GACD,kBAAkB,OAAO;QACvB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,IAAI;YAEtE,MAAM,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,IAAI;YAErD,QAAQ,GAAG,CAAC,CAAC,uFAAuF,EAAE,IAAI;QAC5G,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,GAAG,CAAC,CAAC,EAAE;YACpE,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,OACX,IACA;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,GAAG,OAAO,CAAC,EAAE;YAE1E,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,uBAAuB,EAAE,GAAG,OAAO,CAAC,EACrC;YAGF,QAAQ,GAAG,CAAC,uDAAuD;YAEnE,oCAAoC;YACpC,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,SAAS,IAAI,EAAE;oBACjB,4CAA4C;oBAC5C,OAAO,SAAS,IAAI;gBACtB,OAAO;oBACL,sCAAsC;oBACtC,OAAO;gBACT;YACF;YAEA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,GAAG,CAAC,CAAC,EAAE;YAC5E,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,MAAM;QACR;IACF;IAEA;;GAEC,GACD,gBAAgB,OACd;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,sDAAsD;YAElE,6DAA6D;YAC7D,MAAM,aAAa;gBACjB,GAAG,IAAI;gBACP,aAAa,KAAK,YAAY;YAChC;YAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CACnC,oCACA;YAGF,QAAQ,GAAG,CAAC,0DAA0D;YAEtE,oCAAoC;YACpC,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;oBACvC,iDAAiD;oBACjD,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO,IAAI,SAAS,IAAI,EAAE;oBACxB,4CAA4C;oBAC5C,OAAO,SAAS,IAAI;gBACtB,OAAO;oBACL,sCAAsC;oBACtC,OAAO;gBACT;YACF;YAEA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,MAAM;QACR;IACF;IAEA;;GAEC,GACD,+BAA+B,OAC7B;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,wEAAwE,EAAE,YAAY;YAEnG,+EAA+E;YAC/E,MAAM,cAAc,MAAM,0BAA0B,0BAA0B;YAE9E,MAAM,kBAAkB,YAAY,IAAI,CACtC,CAAC,QAAU,MAAM,qBAAqB,KAAK;YAG7C,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,KAAK,IAAI,gBAAgB,KAAK,CAAC,MAAM,KAAK,GAAG;gBACpF,MAAM,IAAI,MAAM;YAClB;YAEA,qDAAqD;YACrD,MAAM,mBAAmB,gBAAgB,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,iBAAiB,GAAG;YAErF,IAAI,CAAC,kBAAkB;gBACrB,0CAA0C;gBAC1C,OAAO;YACT;YAEA,mCAAmC;YACnC,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,2BAA2B,EAAE,kBAAkB;YAGlD,QAAQ,GAAG,CAAC,yEAAyE;YAErF,oCAAoC;YACpC,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,SAAS,IAAI,EAAE;oBACjB,4CAA4C;oBAC5C,OAAO,SAAS,IAAI;gBACtB,OAAO;oBACL,sCAAsC;oBACtC,OAAO;gBACT;YACF;YAEA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,6CAA6C,EAAE,WAAW,CAAC,CAAC,EAAE;YAC7E,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 7844, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/dsa/hooks/use-dsa-stock-assignments.ts"], "sourcesContent": ["import {\r\n  CreateDsaStockAssignmentRequest,\r\n  ReconcileBatchRequest,\r\n  ReturnStockRequest,\r\n  UpdateDsaStockAssignmentRequest,\r\n} from \"@/types/dsa\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport { DsaStockAssignmentService } from \"../api/dsa-stock-assignment-service\";\r\n\r\n/**\r\n * Hook for fetching DSA stock assignments\r\n */\r\nexport const useDsaStockAssignments = (\r\n  params?: {\r\n    dsa_agent_id?: number;\r\n    branch_id?: number;\r\n    product_id?: number;\r\n  },\r\n  options?: {\r\n    enabled?: boolean;\r\n  }\r\n) => {\r\n  return useQuery({\r\n    queryKey: [\"dsa-stock-assignments\", params],\r\n    queryFn: () => DsaStockAssignmentService.getAssignments(params),\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for fetching a DSA stock assignment by ID\r\n */\r\nexport const useDsaStockAssignment = (\r\n  id: number,\r\n  options?: {\r\n    enabled?: boolean;\r\n  }\r\n) => {\r\n  return useQuery({\r\n    queryKey: [\"dsa-stock-assignment\", id],\r\n    queryFn: () => DsaStockAssignmentService.getAssignmentById(id),\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for fetching unreconciled DSA stock assignments\r\n */\r\nexport const useUnreconciledDsaStockAssignments = (\r\n  dsa_agent_id: number,\r\n  options?: {\r\n    enabled?: boolean;\r\n  }\r\n) => {\r\n  return useQuery({\r\n    queryKey: [\"unreconciled-dsa-stock-assignments\", dsa_agent_id],\r\n    queryFn: () => DsaStockAssignmentService.getUnreconciledAssignments(dsa_agent_id),\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for fetching DSA stock assignments grouped by identifier\r\n */\r\nexport const useDsaStockAssignmentsByIdentifier = (\r\n  params?: {\r\n    branch_id?: number;\r\n  },\r\n  options?: {\r\n    enabled?: boolean;\r\n  }\r\n) => {\r\n  return useQuery({\r\n    queryKey: [\"dsa-stock-assignments-by-identifier\", params],\r\n    queryFn: () => DsaStockAssignmentService.getAssignmentsByIdentifier(params),\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for creating a DSA stock assignment with multiple items\r\n */\r\nexport const useCreateDsaStockAssignment = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateDsaStockAssignmentRequest) =>\r\n      DsaStockAssignmentService.createAssignment(data),\r\n    onSuccess: () => {\r\n      toast.success(\"Stock assignments created successfully\");\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-stock-assignments\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"unreconciled-dsa-stock-assignments\"] });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to create stock assignments\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for updating a DSA stock assignment\r\n */\r\nexport const useUpdateDsaStockAssignment = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: UpdateDsaStockAssignmentRequest) => {\r\n      // If the data includes an id, use that instead of the one from the hook\r\n      const assignmentId = data.id || id;\r\n      // Remove id from the data if it exists to avoid sending it in the request body\r\n      const { id: _, ...requestData } = data;\r\n      return DsaStockAssignmentService.updateAssignment(assignmentId, requestData);\r\n    },\r\n    onSuccess: () => {\r\n      toast.success(\"Stock assignment updated successfully\");\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-stock-assignments\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-stock-assignment\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-stock-assignments-by-identifier\"] });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to update stock assignment\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for deleting a DSA stock assignment\r\n */\r\nexport const useDeleteDsaStockAssignment = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => DsaStockAssignmentService.deleteAssignment(id),\r\n    onSuccess: () => {\r\n      toast.success(\"Stock assignment deleted successfully\");\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-stock-assignments\"] });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to delete stock assignment\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for recording stock return from DSA agent\r\n */\r\nexport const useReturnDsaStock = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: ReturnStockRequest) =>\r\n      DsaStockAssignmentService.returnStock(id, data),\r\n    onSuccess: () => {\r\n      toast.success(\"Stock return recorded successfully\");\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-stock-assignments\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-stock-assignment\", id] });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to record stock return\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for fetching reconciliation details for an assignment identifier\r\n */\r\nexport const useDsaReconciliationByIdentifier = (\r\n  identifier: string,\r\n  options?: {\r\n    enabled?: boolean;\r\n  }\r\n) => {\r\n  return useQuery({\r\n    queryKey: [\"dsa-reconciliation-by-identifier\", identifier],\r\n    queryFn: () => DsaStockAssignmentService.getReconciliationByIdentifier(identifier),\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for reconciling multiple DSA stock assignments in a batch\r\n */\r\nexport const useReconcileDsaStockBatch = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: ReconcileBatchRequest) =>\r\n      DsaStockAssignmentService.reconcileBatch(data),\r\n    onSuccess: () => {\r\n      toast.success(\"Stock reconciliation completed successfully\");\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-stock-assignments\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-stock-assignments-by-identifier\"] });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to reconcile stock\"\r\n      );\r\n    },\r\n  });\r\n};"], "names": [], "mappings": ";;;;;;;;;;;;AAMA;AAAA;AAAA;AACA;AACA;;;;;AAKO,MAAM,yBAAyB,CACpC,QAKA;;IAIA,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAyB;SAAO;QAC3C,OAAO;+CAAE,IAAM,yKAAA,CAAA,4BAAyB,CAAC,cAAc,CAAC;;QACxD,GAAG,OAAO;IACZ;AACF;GAfa;;QAUJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,wBAAwB,CACnC,IACA;;IAIA,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAwB;SAAG;QACtC,OAAO;8CAAE,IAAM,yKAAA,CAAA,4BAAyB,CAAC,iBAAiB,CAAC;;QAC3D,GAAG,OAAO;IACZ;AACF;IAXa;;QAMJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,qCAAqC,CAChD,cACA;;IAIA,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAsC;SAAa;QAC9D,OAAO;2DAAE,IAAM,yKAAA,CAAA,4BAAyB,CAAC,0BAA0B,CAAC;;QACpE,GAAG,OAAO;IACZ;AACF;IAXa;;QAMJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,qCAAqC,CAChD,QAGA;;IAIA,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAuC;SAAO;QACzD,OAAO;2DAAE,IAAM,yKAAA,CAAA,4BAAyB,CAAC,0BAA0B,CAAC;;QACpE,GAAG,OAAO;IACZ;AACF;IAba;;QAQJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,8BAA8B;;IACzC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;uDAAE,CAAC,OACX,yKAAA,CAAA,4BAAyB,CAAC,gBAAgB,CAAC;;QAC7C,SAAS;uDAAE;gBACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAwB;gBAAC;gBACpE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAqC;gBAAC;YACnF;;QACA,OAAO;uDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;YAErC;;IACF;AACF;IAjBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmBb,MAAM,8BAA8B,CAAC;;IAC1C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;uDAAE,CAAC;gBACX,wEAAwE;gBACxE,MAAM,eAAe,KAAK,EAAE,IAAI;gBAChC,+EAA+E;gBAC/E,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,aAAa,GAAG;gBAClC,OAAO,yKAAA,CAAA,4BAAyB,CAAC,gBAAgB,CAAC,cAAc;YAClE;;QACA,SAAS;uDAAE;gBACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAwB;gBAAC;gBACpE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAwB;qBAAG;gBAAC;gBACvE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAsC;gBAAC;YACpF;;QACA,OAAO;uDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;YAErC;;IACF;AACF;IAvBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAyBb,MAAM,8BAA8B;;IACzC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;uDAAE,CAAC,KAAe,yKAAA,CAAA,4BAAyB,CAAC,gBAAgB,CAAC;;QACvE,SAAS;uDAAE;gBACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAwB;gBAAC;YACtE;;QACA,OAAO;uDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;YAErC;;IACF;AACF;IAfa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,MAAM,oBAAoB,CAAC;;IAChC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE,CAAC,OACX,yKAAA,CAAA,4BAAyB,CAAC,WAAW,CAAC,IAAI;;QAC5C,SAAS;6CAAE;gBACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAwB;gBAAC;gBACpE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAwB;qBAAG;gBAAC;YACzE;;QACA,OAAO;6CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;YAErC;;IACF;AACF;IAjBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmBb,MAAM,mCAAmC,CAC9C,YACA;;IAIA,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAoC;SAAW;QAC1D,OAAO;yDAAE,IAAM,yKAAA,CAAA,4BAAyB,CAAC,6BAA6B,CAAC;;QACvE,GAAG,OAAO;IACZ;AACF;IAXa;;QAMJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,4BAA4B;;IACvC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qDAAE,CAAC,OACX,yKAAA,CAAA,4BAAyB,CAAC,cAAc,CAAC;;QAC3C,SAAS;qDAAE;gBACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAwB;gBAAC;gBACpE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAsC;gBAAC;YACpF;;QACA,OAAO;qDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;YAErC;;IACF;AACF;IAjBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 8147, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/dsa/api/dsa-customer-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { CreateDsaCustomerRequest, DsaCustomer, UpdateDsaCustomerRequest } from \"@/types/dsa\";\r\nimport { AxiosError } from \"axios\";\r\n\r\n/**\r\n * DSA Customer Service\r\n *\r\n * This service handles API calls related to DSA customers (customers with is_dsa=true)\r\n */\r\nexport const DsaCustomerService = {\r\n  /**\r\n   * Get all DSA customers\r\n   */\r\n  getCustomers: async (params?: {\r\n    tenant_id?: number;\r\n    branch_id?: number;\r\n    dsa_type?: number; // Optional filter for specific DSA type (1 or 2)\r\n  }): Promise<DsaCustomer[]> => {\r\n    // Prepare query parameters\r\n    const queryParams: any = { ...params };\r\n\r\n    console.log('DSA Customer Service - getCustomers - params:', params);\r\n\r\n    try {\r\n      // Call the DSA customers endpoint\r\n      const response = await apiClient.get<any>(\"/dsa-customers\", {\r\n        params: queryParams\r\n      });\r\n      console.log('DSA Customer Service - getCustomers - response:', response);\r\n\r\n      // Extract customers from the response\r\n      let customers: DsaCustomer[] = [];\r\n\r\n      // Handle different response formats\r\n      if (response) {\r\n        if (Array.isArray(response)) {\r\n          // If response is directly an array\r\n          customers = response;\r\n        } else if (typeof response === 'object') {\r\n          if (Array.isArray(response.data)) {\r\n            // If response.data is an array\r\n            customers = response.data;\r\n          } else if (response.data && typeof response.data === 'object') {\r\n            if (Array.isArray(response.data.data)) {\r\n              // If response.data.data is an array (nested data property)\r\n              customers = response.data.data;\r\n            } else if (response.data.customers && Array.isArray(response.data.customers)) {\r\n              // If response.data.customers is an array\r\n              customers = response.data.customers;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log('Raw response:', response);\r\n      console.log('Extracted customers:', customers);\r\n\r\n      // If customers is still empty, try to find any array in the response\r\n      if (customers.length === 0 && response) {\r\n        console.log('No customers found in standard locations, searching deeper in response');\r\n\r\n        // Function to recursively search for arrays in the response\r\n        const findArrays = (obj: any, path = ''): {path: string, array: any[]}[] => {\r\n          if (!obj || typeof obj !== 'object') return [];\r\n\r\n          let results: {path: string, array: any[]}[] = [];\r\n\r\n          Object.keys(obj).forEach(key => {\r\n            const newPath = path ? `${path}.${key}` : key;\r\n\r\n            if (Array.isArray(obj[key])) {\r\n              results.push({path: newPath, array: obj[key]});\r\n            } else if (typeof obj[key] === 'object' && obj[key] !== null) {\r\n              results = [...results, ...findArrays(obj[key], newPath)];\r\n            }\r\n          });\r\n\r\n          return results;\r\n        };\r\n\r\n        const arrays = findArrays(response);\r\n        console.log('Found arrays in response:', arrays);\r\n\r\n        // Use the first array that looks like it contains customers\r\n        for (const {path, array} of arrays) {\r\n          if (array.length > 0 &&\r\n              array[0] &&\r\n              typeof array[0] === 'object' &&\r\n              ('name' in array[0] || 'id' in array[0])) {\r\n            console.log(`Using array found at path: ${path}`);\r\n            customers = array;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Process the customers to add a dsa_type display property\r\n      customers = customers.map(customer => {\r\n        // Ensure dsa_type is always 1 or 2, default to 1 if invalid\r\n        const dsaType = customer.dsa_type === 1 || customer.dsa_type === 2 ? customer.dsa_type : 1;\r\n\r\n        // Log the stock_limit value to see what format it's in\r\n        console.log(`Customer ${customer.id} (${customer.name}) stock_limit:`, customer.stock_limit,\r\n                    'type:', typeof customer.stock_limit);\r\n\r\n        return {\r\n          ...customer,\r\n          dsa_type: dsaType, // Override with validated value\r\n          dsa_type_display: dsaType === 1 ? 'Normal DSA' : 'Van DSA'\r\n        };\r\n      });\r\n\r\n      console.log('DSA Customer Service - getCustomers - processed customers:', customers);\r\n      console.log(`DSA Customer Service - getCustomers - found ${customers.length} customers for branch_id: ${params?.branch_id}`);\r\n\r\n      return customers;\r\n    } catch (error) {\r\n      console.error('Error fetching DSA customers:', error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      return [];\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a DSA customer by ID\r\n   */\r\n  getCustomerById: async (id: number): Promise<DsaCustomer> => {\r\n    try {\r\n      console.log(`DSA Customer Service - getCustomerById - fetching customer ID: ${id}`);\r\n\r\n      const response = await apiClient.get<DsaCustomer>(`/dsa-customers/${id}`);\r\n      console.log('DSA Customer Service - getCustomerById - response:', response);\r\n\r\n      // Handle different response formats\r\n      let customer: DsaCustomer;\r\n\r\n      if (response && typeof response === 'object') {\r\n        if (response.data) {\r\n          // If response has a data property, use that\r\n          customer = response.data;\r\n        } else {\r\n          // Otherwise use the response directly\r\n          customer = response;\r\n        }\r\n      } else {\r\n        throw new Error('Invalid response format');\r\n      }\r\n\r\n      // Ensure dsa_type is valid and add the dsa_type_display property\r\n      const dsaType = customer.dsa_type === 1 || customer.dsa_type === 2 ? customer.dsa_type : 1;\r\n\r\n      return {\r\n        ...customer,\r\n        dsa_type: dsaType, // Override with validated value\r\n        dsa_type_display: dsaType === 1 ? 'Normal DSA' : 'Van DSA'\r\n      };\r\n    } catch (error) {\r\n      console.error(`Error fetching DSA customer with ID ${id}:`, error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new DSA customer\r\n   */\r\n  createCustomer: async (data: CreateDsaCustomerRequest): Promise<DsaCustomer> => {\r\n    try {\r\n      // Ensure we're sending the required fields to the API\r\n      const apiRequest = {\r\n        name: data.name,\r\n        phone: data.phone,\r\n        email: data.email || null,\r\n        address: data.address || null,\r\n        branch_id: data.branch_id,\r\n        dsa_type: data.dsa_type, // This should be 1 for normal DSA or 2 for van DSA\r\n        stock_limit: data.stock_limit || null,\r\n        // tenant_id will be automatically set on the backend if not provided\r\n        ...(data.tenant_id && { tenant_id: data.tenant_id }),\r\n      };\r\n\r\n      console.log('DSA Customer Service - createCustomer - request:', apiRequest);\r\n\r\n      const response = await apiClient.post<DsaCustomer>(\"/dsa-customers\", apiRequest);\r\n      console.log('DSA Customer Service - createCustomer - response:', response);\r\n\r\n      // Handle different response formats\r\n      let customer: DsaCustomer;\r\n\r\n      if (response && typeof response === 'object') {\r\n        if (response.data) {\r\n          // If response has a data property, use that\r\n          customer = response.data.data || response.data;\r\n        } else {\r\n          // Otherwise use the response directly\r\n          customer = response;\r\n        }\r\n      } else {\r\n        throw new Error('Invalid response format');\r\n      }\r\n\r\n      // Ensure dsa_type is valid and add the dsa_type_display property\r\n      const dsaType = customer.dsa_type === 1 || customer.dsa_type === 2 ? customer.dsa_type : 1;\r\n\r\n      return {\r\n        ...customer,\r\n        dsa_type: dsaType, // Override with validated value\r\n        dsa_type_display: dsaType === 1 ? 'Normal DSA' : 'Van DSA'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error creating DSA customer:', error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update a DSA customer\r\n   */\r\n  updateCustomer: async (id: number, data: UpdateDsaCustomerRequest): Promise<DsaCustomer> => {\r\n    try {\r\n      console.log(`DSA Customer Service - updateCustomer - updating customer ID: ${id} with data:`, data);\r\n\r\n      const response = await apiClient.put<DsaCustomer>(`/dsa-customers/${id}`, data);\r\n      console.log('DSA Customer Service - updateCustomer - response:', response);\r\n\r\n      // Handle different response formats\r\n      let customer: DsaCustomer;\r\n\r\n      if (response && typeof response === 'object') {\r\n        if (response.data) {\r\n          // If response has a data property, use that\r\n          customer = response.data;\r\n        } else {\r\n          // Otherwise use the response directly\r\n          customer = response;\r\n        }\r\n      } else {\r\n        throw new Error('Invalid response format');\r\n      }\r\n\r\n      // Ensure dsa_type is valid and add the dsa_type_display property\r\n      const dsaType = customer.dsa_type === 1 || customer.dsa_type === 2 ? customer.dsa_type : 1;\r\n\r\n      return {\r\n        ...customer,\r\n        dsa_type: dsaType, // Override with validated value\r\n        dsa_type_display: dsaType === 1 ? 'Normal DSA' : 'Van DSA'\r\n      };\r\n    } catch (error) {\r\n      console.error(`Error updating DSA customer with ID ${id}:`, error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete a DSA customer\r\n   */\r\n  deleteCustomer: async (id: number): Promise<void> => {\r\n    try {\r\n      console.log(`DSA Customer Service - deleteCustomer - deleting customer ID: ${id}`);\r\n\r\n      await apiClient.delete(`/dsa-customers/${id}`);\r\n      console.log('DSA Customer Service - deleteCustomer - successful');\r\n    } catch (error) {\r\n      console.error(`Error deleting DSA customer with ID ${id}:`, error);\r\n      const axiosError = error as AxiosError;\r\n      console.error('Error details:', axiosError.response?.data || (error as Error).message || String(error));\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AASO,MAAM,qBAAqB;IAChC;;GAEC,GACD,cAAc,OAAO;QAKnB,2BAA2B;QAC3B,MAAM,cAAmB;YAAE,GAAG,MAAM;QAAC;QAErC,QAAQ,GAAG,CAAC,iDAAiD;QAE7D,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,kBAAkB;gBAC1D,QAAQ;YACV;YACA,QAAQ,GAAG,CAAC,mDAAmD;YAE/D,sCAAsC;YACtC,IAAI,YAA2B,EAAE;YAEjC,oCAAoC;YACpC,IAAI,UAAU;gBACZ,IAAI,MAAM,OAAO,CAAC,WAAW;oBAC3B,mCAAmC;oBACnC,YAAY;gBACd,OAAO,IAAI,OAAO,aAAa,UAAU;oBACvC,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;wBAChC,+BAA+B;wBAC/B,YAAY,SAAS,IAAI;oBAC3B,OAAO,IAAI,SAAS,IAAI,IAAI,OAAO,SAAS,IAAI,KAAK,UAAU;wBAC7D,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;4BACrC,2DAA2D;4BAC3D,YAAY,SAAS,IAAI,CAAC,IAAI;wBAChC,OAAO,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,SAAS,GAAG;4BAC5E,yCAAyC;4BACzC,YAAY,SAAS,IAAI,CAAC,SAAS;wBACrC;oBACF;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,iBAAiB;YAC7B,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,qEAAqE;YACrE,IAAI,UAAU,MAAM,KAAK,KAAK,UAAU;gBACtC,QAAQ,GAAG,CAAC;gBAEZ,4DAA4D;gBAC5D,MAAM,aAAa,CAAC,KAAU,OAAO,EAAE;oBACrC,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU,OAAO,EAAE;oBAE9C,IAAI,UAA0C,EAAE;oBAEhD,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,CAAA;wBACvB,MAAM,UAAU,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,GAAG;wBAE1C,IAAI,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG;4BAC3B,QAAQ,IAAI,CAAC;gCAAC,MAAM;gCAAS,OAAO,GAAG,CAAC,IAAI;4BAAA;wBAC9C,OAAO,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,YAAY,GAAG,CAAC,IAAI,KAAK,MAAM;4BAC5D,UAAU;mCAAI;mCAAY,WAAW,GAAG,CAAC,IAAI,EAAE;6BAAS;wBAC1D;oBACF;oBAEA,OAAO;gBACT;gBAEA,MAAM,SAAS,WAAW;gBAC1B,QAAQ,GAAG,CAAC,6BAA6B;gBAEzC,4DAA4D;gBAC5D,KAAK,MAAM,EAAC,IAAI,EAAE,KAAK,EAAC,IAAI,OAAQ;oBAClC,IAAI,MAAM,MAAM,GAAG,KACf,KAAK,CAAC,EAAE,IACR,OAAO,KAAK,CAAC,EAAE,KAAK,YACpB,CAAC,UAAU,KAAK,CAAC,EAAE,IAAI,QAAQ,KAAK,CAAC,EAAE,GAAG;wBAC5C,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,MAAM;wBAChD,YAAY;wBACZ;oBACF;gBACF;YACF;YAEA,2DAA2D;YAC3D,YAAY,UAAU,GAAG,CAAC,CAAA;gBACxB,4DAA4D;gBAC5D,MAAM,UAAU,SAAS,QAAQ,KAAK,KAAK,SAAS,QAAQ,KAAK,IAAI,SAAS,QAAQ,GAAG;gBAEzF,uDAAuD;gBACvD,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC,EAAE,SAAS,WAAW,EAC/E,SAAS,OAAO,SAAS,WAAW;gBAEhD,OAAO;oBACL,GAAG,QAAQ;oBACX,UAAU;oBACV,kBAAkB,YAAY,IAAI,eAAe;gBACnD;YACF;YAEA,QAAQ,GAAG,CAAC,8DAA8D;YAC1E,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,UAAU,MAAM,CAAC,0BAA0B,EAAE,QAAQ,WAAW;YAE3H,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,iBAAiB,OAAO;QACtB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,+DAA+D,EAAE,IAAI;YAElF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAc,CAAC,eAAe,EAAE,IAAI;YACxE,QAAQ,GAAG,CAAC,sDAAsD;YAElE,oCAAoC;YACpC,IAAI;YAEJ,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,SAAS,IAAI,EAAE;oBACjB,4CAA4C;oBAC5C,WAAW,SAAS,IAAI;gBAC1B,OAAO;oBACL,sCAAsC;oBACtC,WAAW;gBACb;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,iEAAiE;YACjE,MAAM,UAAU,SAAS,QAAQ,KAAK,KAAK,SAAS,QAAQ,KAAK,IAAI,SAAS,QAAQ,GAAG;YAEzF,OAAO;gBACL,GAAG,QAAQ;gBACX,UAAU;gBACV,kBAAkB,YAAY,IAAI,eAAe;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC5D,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,MAAM;QACR;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,sDAAsD;YACtD,MAAM,aAAa;gBACjB,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK,IAAI;gBACrB,SAAS,KAAK,OAAO,IAAI;gBACzB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,aAAa,KAAK,WAAW,IAAI;gBACjC,qEAAqE;gBACrE,GAAI,KAAK,SAAS,IAAI;oBAAE,WAAW,KAAK,SAAS;gBAAC,CAAC;YACrD;YAEA,QAAQ,GAAG,CAAC,oDAAoD;YAEhE,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAc,kBAAkB;YACrE,QAAQ,GAAG,CAAC,qDAAqD;YAEjE,oCAAoC;YACpC,IAAI;YAEJ,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,SAAS,IAAI,EAAE;oBACjB,4CAA4C;oBAC5C,WAAW,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;gBAChD,OAAO;oBACL,sCAAsC;oBACtC,WAAW;gBACb;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,iEAAiE;YACjE,MAAM,UAAU,SAAS,QAAQ,KAAK,KAAK,SAAS,QAAQ,KAAK,IAAI,SAAS,QAAQ,GAAG;YAEzF,OAAO;gBACL,GAAG,QAAQ;gBACX,UAAU;gBACV,kBAAkB,YAAY,IAAI,eAAe;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,MAAM;QACR;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO,IAAY;QACjC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,8DAA8D,EAAE,GAAG,WAAW,CAAC,EAAE;YAE9F,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAc,CAAC,eAAe,EAAE,IAAI,EAAE;YAC1E,QAAQ,GAAG,CAAC,qDAAqD;YAEjE,oCAAoC;YACpC,IAAI;YAEJ,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,IAAI,SAAS,IAAI,EAAE;oBACjB,4CAA4C;oBAC5C,WAAW,SAAS,IAAI;gBAC1B,OAAO;oBACL,sCAAsC;oBACtC,WAAW;gBACb;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,iEAAiE;YACjE,MAAM,UAAU,SAAS,QAAQ,KAAK,KAAK,SAAS,QAAQ,KAAK,IAAI,SAAS,QAAQ,GAAG;YAEzF,OAAO;gBACL,GAAG,QAAQ;gBACX,UAAU;gBACV,kBAAkB,YAAY,IAAI,eAAe;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC5D,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,MAAM;QACR;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,8DAA8D,EAAE,IAAI;YAEjF,MAAM,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;YAC7C,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC5D,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,kBAAkB,WAAW,QAAQ,EAAE,QAAQ,AAAC,MAAgB,OAAO,IAAI,OAAO;YAChG,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 8387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/dsa/hooks/use-dsa-agents.ts"], "sourcesContent": ["import { CreateDsaAgentRequest, UpdateDsaAgentRequest } from \"@/types/dsa\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport { DsaCustomerService } from \"../api/dsa-customer-service\";\r\n\r\n// This file is maintained for backward compatibility\r\n// It now uses the DsaCustomerService instead of DsaAgentService\r\n\r\n/**\r\n * Hook for fetching DSA agents\r\n */\r\nexport const useDsaAgents = (\r\n  params?: {\r\n    tenant_id?: number;\r\n    branch_id?: number;\r\n    dsa_type?: number;\r\n  },\r\n  options?: {\r\n    enabled?: boolean;\r\n  }\r\n) => {\r\n  return useQuery({\r\n    queryKey: [\"dsa-agents\", params],\r\n    queryFn: () => DsaCustomerService.getCustomers(params),\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for fetching a DSA agent by ID\r\n */\r\nexport const useDsaAgent = (\r\n  id: number,\r\n  options?: {\r\n    enabled?: boolean;\r\n  }\r\n) => {\r\n  return useQuery({\r\n    queryKey: [\"dsa-agent\", id],\r\n    queryFn: () => DsaCustomerService.getCustomerById(id),\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for creating a DSA agent\r\n */\r\nexport const useCreateDsaAgent = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateDsaAgentRequest) =>\r\n      DsaCustomerService.createCustomer(data),\r\n    onSuccess: () => {\r\n      toast.success(\"DSA agent created successfully\");\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-agents\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-customers\"] });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to create DSA agent\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for updating a DSA agent\r\n */\r\nexport const useUpdateDsaAgent = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: UpdateDsaAgentRequest) =>\r\n      DsaCustomerService.updateCustomer(id, data),\r\n    onSuccess: () => {\r\n      toast.success(\"DSA agent updated successfully\");\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-agents\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-agent\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-customers\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-customer\", id] });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to update DSA agent\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for deleting a DSA agent\r\n */\r\nexport const useDeleteDsaAgent = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => DsaCustomerService.deleteCustomer(id),\r\n    onSuccess: () => {\r\n      toast.success(\"DSA agent deleted successfully\");\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-agents\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"dsa-customers\"] });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to delete DSA agent\"\r\n      );\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AACA;AAAA;AAAA;AACA;AACA;;;;;AAQO,MAAM,eAAe,CAC1B,QAKA;;IAIA,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAc;SAAO;QAChC,OAAO;qCAAE,IAAM,8JAAA,CAAA,qBAAkB,CAAC,YAAY,CAAC;;QAC/C,GAAG,OAAO;IACZ;AACF;GAfa;;QAUJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,cAAc,CACzB,IACA;;IAIA,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAa;SAAG;QAC3B,OAAO;oCAAE,IAAM,8JAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;;QAClD,GAAG,OAAO;IACZ;AACF;IAXa;;QAMJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,oBAAoB;;IAC/B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE,CAAC,OACX,8JAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;;QACpC,SAAS;6CAAE;gBACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAa;gBAAC;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAgB;gBAAC;YAC9D;;QACA,OAAO;6CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;YAErC;;IACF;AACF;IAjBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmBb,MAAM,oBAAoB,CAAC;;IAChC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE,CAAC,OACX,8JAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,IAAI;;QACxC,SAAS;6CAAE;gBACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAa;gBAAC;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAa;qBAAG;gBAAC;gBAC5D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAgB;gBAAC;gBAC5D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAgB;qBAAG;gBAAC;YACjE;;QACA,OAAO;6CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;YAErC;;IACF;AACF;IAnBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAqBb,MAAM,oBAAoB;;IAC/B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE,CAAC,KAAe,8JAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;;QAC9D,SAAS;6CAAE;gBACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAa;gBAAC;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAgB;gBAAC;YAC9D;;QACA,OAAO;6CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;YAErC;;IACF;AACF;IAhBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 8565, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { CheckIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Checkbox({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\r\n  return (\r\n    <CheckboxPrimitive.Root\r\n      data-slot=\"checkbox\"\r\n      className={cn(\r\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <CheckboxPrimitive.Indicator\r\n        data-slot=\"checkbox-indicator\"\r\n        className=\"flex items-center justify-center text-current transition-none\"\r\n      >\r\n        <CheckIcon className=\"size-3.5\" />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 8616, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 8650, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/inventory/components/dsa-stock-table.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\r\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from \"@/components/ui/card\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { useDsaStockAssignmentsByIdentifier } from \"@/features/dsa/hooks/use-dsa-stock-assignments\";\r\nimport { useDsaAgents } from \"@/features/dsa/hooks/use-dsa-agents\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { DsaStockAssignment } from \"@/types/dsa\";\r\nimport { AlertTriangle, DollarSign, Package, UserCheck } from \"lucide-react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Pagination } from \"@/components/ui/pagination\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport Link from \"next/link\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { Label } from \"@/components/ui/label\";\r\n\r\ninterface DsaStockTableProps {\r\n  branchId?: number;\r\n}\r\n\r\nexport function DsaStockTable({ branchId }: DsaStockTableProps) {\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [showOnlyUnreconciled, setShowOnlyUnreconciled] = useState(false);\r\n  const itemsPerPage = 10;\r\n\r\n  // Get current user to determine role\r\n  const { data: userData } = useCurrentUser();\r\n\r\n  // Check if user is admin (company_admin, super_admin, tenant_admin)\r\n  const isAdminUser = userData?.role_name &&\r\n    [\"super_admin\", \"company_admin\", \"tenant_admin\"].includes(userData.role_name);\r\n\r\n  // For branch managers, we need their branch ID\r\n  const userBranchId = userData?.branch?.id;\r\n\r\n  // Determine which branch ID to use for filtering\r\n  // If admin user and branchId is undefined (All Branches selected), don't filter by branch\r\n  // Otherwise use the provided branchId or the user's branch ID for branch managers\r\n  const effectiveBranchId = isAdminUser && branchId === undefined\r\n    ? undefined\r\n    : branchId || userBranchId;\r\n\r\n  console.log(\"DSA Stock Table - User role:\", userData?.role_name);\r\n  console.log(\"DSA Stock Table - Is admin:\", isAdminUser);\r\n  console.log(\"DSA Stock Table - User branch ID:\", userBranchId);\r\n  console.log(\"DSA Stock Table - Provided branch ID:\", branchId);\r\n  console.log(\"DSA Stock Table - Effective branch ID:\", effectiveBranchId);\r\n\r\n  // Fetch DSA stock assignments grouped by identifier\r\n  const { data: assignmentsData, isLoading: isLoadingAssignments } =\r\n    useDsaStockAssignmentsByIdentifier(\r\n      { branch_id: effectiveBranchId },\r\n      { enabled: true } // Always enable the query, we'll filter the results as needed\r\n    );\r\n\r\n  // Fetch DSA agents\r\n  const { data: dsaAgentsData, isLoading: isLoadingAgents } = useDsaAgents();\r\n\r\n  // Process the assignments data\r\n  const assignments = assignmentsData || [];\r\n\r\n  // Apply role-based filtering\r\n  const roleFilteredAssignments = assignments.filter((assignment) => {\r\n    // For super_admin, company_admin, and tenant_admin, show all assignments\r\n    if (isAdminUser) {\r\n      return true;\r\n    }\r\n\r\n    // For branch managers, show only assignments from their branch\r\n    if (userData?.role_name === \"branch_manager\") {\r\n      return assignment.branch_id === userBranchId;\r\n    }\r\n\r\n    // Default case - show all\r\n    return true;\r\n  });\r\n\r\n  // Apply unreconciled filter if enabled\r\n  const reconciliationFilteredAssignments = showOnlyUnreconciled\r\n    ? roleFilteredAssignments.filter(assignment => !assignment.is_reconciled)\r\n    : roleFilteredAssignments;\r\n\r\n  console.log(\"DSA Stock Table - Total assignments:\", assignments.length);\r\n  console.log(\"DSA Stock Table - After role filtering:\", roleFilteredAssignments.length);\r\n  console.log(\"DSA Stock Table - After reconciliation filtering:\", reconciliationFilteredAssignments.length);\r\n\r\n  // Filter assignments based on search query\r\n  const filteredAssignments = reconciliationFilteredAssignments.filter((assignment) => {\r\n    const searchLower = searchQuery.toLowerCase();\r\n    return (\r\n      assignment.dsa_agent_name?.toLowerCase().includes(searchLower) ||\r\n      assignment.assignment_identifier?.toLowerCase().includes(searchLower) ||\r\n      assignment.branch_name?.toLowerCase().includes(searchLower)\r\n    );\r\n  });\r\n\r\n  // Paginate the filtered assignments\r\n  const paginatedAssignments = filteredAssignments.slice(\r\n    (currentPage - 1) * itemsPerPage,\r\n    currentPage * itemsPerPage\r\n  );\r\n\r\n  // Calculate total pages\r\n  const totalPages = Math.ceil(filteredAssignments.length / itemsPerPage);\r\n\r\n  // Calculate statistics\r\n  const totalAssignments = assignments.length;\r\n  const totalItems = assignments.reduce((total, assignment) => {\r\n    return total + (assignment.items?.length || 0);\r\n  }, 0);\r\n\r\n  const totalQuantity = assignments.reduce((total, assignment) => {\r\n    return total + (assignment.items?.reduce((sum, item) => sum + (item.quantity_assigned || 0), 0) || 0);\r\n  }, 0);\r\n\r\n  const totalValue = assignments.reduce((total, assignment) => {\r\n    return total + (assignment.items?.reduce((sum, item) => {\r\n      const price = item.Product?.price || 0;\r\n      return sum + (price * (item.quantity_assigned || 0));\r\n    }, 0) || 0);\r\n  }, 0);\r\n\r\n  // Handle search\r\n  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setSearchQuery(e.target.value);\r\n    setCurrentPage(1); // Reset to first page when searching\r\n  };\r\n\r\n  // Handle page change\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  // Reset to page 1 when filters change\r\n  useEffect(() => {\r\n    setCurrentPage(1);\r\n  }, [showOnlyUnreconciled, branchId]);\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Statistics Cards */}\r\n      <div className=\"grid gap-4 md:grid-cols-4\">\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium\">\r\n              DSA Agents\r\n            </CardTitle>\r\n            <UserCheck className=\"h-4 w-4 text-muted-foreground\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold\">\r\n              {isLoadingAgents ? \"...\" : dsaAgentsData?.length || 0}\r\n            </div>\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              Active DSA agents\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium\">\r\n              Assignments\r\n            </CardTitle>\r\n            <Package className=\"h-4 w-4 text-muted-foreground\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold\">\r\n              {isLoadingAssignments ? \"...\" : totalAssignments}\r\n            </div>\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              Total stock assignments\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium\">\r\n              Items Assigned\r\n            </CardTitle>\r\n            <AlertTriangle className=\"h-4 w-4 text-amber-500\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold\">\r\n              {isLoadingAssignments ? \"...\" : totalItems}\r\n            </div>\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              Total quantity: {totalQuantity}\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium\">\r\n              Total Value\r\n            </CardTitle>\r\n            <DollarSign className=\"h-4 w-4 text-green-500\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold\">\r\n              {isLoadingAssignments ? \"...\" : formatCurrency(totalValue)}\r\n            </div>\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              Value of assigned stock\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Search and Actions */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"w-full max-w-sm\">\r\n            <Input\r\n              placeholder=\"Search assignments...\"\r\n              value={searchQuery}\r\n              onChange={handleSearch}\r\n              className=\"w-full\"\r\n            />\r\n          </div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Checkbox\r\n              id=\"unreconciled\"\r\n              checked={showOnlyUnreconciled}\r\n              onCheckedChange={(checked) => setShowOnlyUnreconciled(checked === true)}\r\n            />\r\n            <Label htmlFor=\"unreconciled\">Show only unreconciled</Label>\r\n          </div>\r\n        </div>\r\n        <Button asChild>\r\n          <Link href=\"/dsa/assignments\">Manage Assignments</Link>\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Assignments Table */}\r\n      <div className=\"rounded-md border\">\r\n        <Table>\r\n          <TableHeader>\r\n            <TableRow>\r\n              <TableHead>Assignment ID</TableHead>\r\n              <TableHead>DSA Agent</TableHead>\r\n              <TableHead>Branch</TableHead>\r\n              <TableHead>Items</TableHead>\r\n              <TableHead>Total Quantity</TableHead>\r\n              <TableHead>Status</TableHead>\r\n              <TableHead>Actions</TableHead>\r\n            </TableRow>\r\n          </TableHeader>\r\n          <TableBody>\r\n            {isLoadingAssignments ? (\r\n              // Loading skeleton\r\n              Array.from({ length: 5 }).map((_, index) => (\r\n                <TableRow key={`loading-${index}`}>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-24\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-32\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-32\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-16\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-16\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-4 w-20\" />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Skeleton className=\"h-8 w-20\" />\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))\r\n            ) : paginatedAssignments.length > 0 ? (\r\n              paginatedAssignments.map((assignment) => (\r\n                <TableRow key={assignment.assignment_identifier}>\r\n                  <TableCell className=\"font-medium\">\r\n                    {assignment.assignment_identifier}\r\n                  </TableCell>\r\n                  <TableCell>{assignment.dsa_agent_name}</TableCell>\r\n                  <TableCell>{assignment.branch_name}</TableCell>\r\n                  <TableCell>{assignment.items?.length || 0}</TableCell>\r\n                  <TableCell>\r\n                    {assignment.items?.reduce(\r\n                      (sum, item) => sum + (item.quantity_assigned || 0),\r\n                      0\r\n                    ) || 0}\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Badge\r\n                      variant={\r\n                        assignment.is_reconciled ? \"success\" : \"secondary\"\r\n                      }\r\n                    >\r\n                      {assignment.is_reconciled ? \"Reconciled\" : \"Unreconciled\"}\r\n                    </Badge>\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      asChild\r\n                    >\r\n                      <Link href={`/dsa/assignments/${assignment.assignment_identifier}`}>\r\n                        View\r\n                      </Link>\r\n                    </Button>\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))\r\n            ) : (\r\n              <TableRow>\r\n                <TableCell colSpan={7} className=\"h-24 text-center\">\r\n                  {searchQuery\r\n                    ? \"No assignments found matching your search.\"\r\n                    : \"No DSA stock assignments found.\"}\r\n                </TableCell>\r\n              </TableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {totalPages > 1 && (\r\n        <Pagination\r\n          currentPage={currentPage}\r\n          totalPages={totalPages}\r\n          onPageChange={handlePageChange}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAQA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAsB;;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,eAAe;IAErB,qCAAqC;IACrC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAExC,oEAAoE;IACpE,MAAM,cAAc,UAAU,aAC5B;QAAC;QAAe;QAAiB;KAAe,CAAC,QAAQ,CAAC,SAAS,SAAS;IAE9E,+CAA+C;IAC/C,MAAM,eAAe,UAAU,QAAQ;IAEvC,iDAAiD;IACjD,0FAA0F;IAC1F,kFAAkF;IAClF,MAAM,oBAAoB,eAAe,aAAa,YAClD,YACA,YAAY;IAEhB,QAAQ,GAAG,CAAC,gCAAgC,UAAU;IACtD,QAAQ,GAAG,CAAC,+BAA+B;IAC3C,QAAQ,GAAG,CAAC,qCAAqC;IACjD,QAAQ,GAAG,CAAC,yCAAyC;IACrD,QAAQ,GAAG,CAAC,0CAA0C;IAEtD,oDAAoD;IACpD,MAAM,EAAE,MAAM,eAAe,EAAE,WAAW,oBAAoB,EAAE,GAC9D,CAAA,GAAA,wKAAA,CAAA,qCAAkC,AAAD,EAC/B;QAAE,WAAW;IAAkB,GAC/B;QAAE,SAAS;IAAK,EAAE,8DAA8D;;IAGpF,mBAAmB;IACnB,MAAM,EAAE,MAAM,aAAa,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,eAAY,AAAD;IAEvE,+BAA+B;IAC/B,MAAM,cAAc,mBAAmB,EAAE;IAEzC,6BAA6B;IAC7B,MAAM,0BAA0B,YAAY,MAAM,CAAC,CAAC;QAClD,yEAAyE;QACzE,IAAI,aAAa;YACf,OAAO;QACT;QAEA,+DAA+D;QAC/D,IAAI,UAAU,cAAc,kBAAkB;YAC5C,OAAO,WAAW,SAAS,KAAK;QAClC;QAEA,0BAA0B;QAC1B,OAAO;IACT;IAEA,uCAAuC;IACvC,MAAM,oCAAoC,uBACtC,wBAAwB,MAAM,CAAC,CAAA,aAAc,CAAC,WAAW,aAAa,IACtE;IAEJ,QAAQ,GAAG,CAAC,wCAAwC,YAAY,MAAM;IACtE,QAAQ,GAAG,CAAC,2CAA2C,wBAAwB,MAAM;IACrF,QAAQ,GAAG,CAAC,qDAAqD,kCAAkC,MAAM;IAEzG,2CAA2C;IAC3C,MAAM,sBAAsB,kCAAkC,MAAM,CAAC,CAAC;QACpE,MAAM,cAAc,YAAY,WAAW;QAC3C,OACE,WAAW,cAAc,EAAE,cAAc,SAAS,gBAClD,WAAW,qBAAqB,EAAE,cAAc,SAAS,gBACzD,WAAW,WAAW,EAAE,cAAc,SAAS;IAEnD;IAEA,oCAAoC;IACpC,MAAM,uBAAuB,oBAAoB,KAAK,CACpD,CAAC,cAAc,CAAC,IAAI,cACpB,cAAc;IAGhB,wBAAwB;IACxB,MAAM,aAAa,KAAK,IAAI,CAAC,oBAAoB,MAAM,GAAG;IAE1D,uBAAuB;IACvB,MAAM,mBAAmB,YAAY,MAAM;IAC3C,MAAM,aAAa,YAAY,MAAM,CAAC,CAAC,OAAO;QAC5C,OAAO,QAAQ,CAAC,WAAW,KAAK,EAAE,UAAU,CAAC;IAC/C,GAAG;IAEH,MAAM,gBAAgB,YAAY,MAAM,CAAC,CAAC,OAAO;QAC/C,OAAO,QAAQ,CAAC,WAAW,KAAK,EAAE,OAAO,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,iBAAiB,IAAI,CAAC,GAAG,MAAM,CAAC;IACtG,GAAG;IAEH,MAAM,aAAa,YAAY,MAAM,CAAC,CAAC,OAAO;QAC5C,OAAO,QAAQ,CAAC,WAAW,KAAK,EAAE,OAAO,CAAC,KAAK;YAC7C,MAAM,QAAQ,KAAK,OAAO,EAAE,SAAS;YACrC,OAAO,MAAO,QAAQ,CAAC,KAAK,iBAAiB,IAAI,CAAC;QACpD,GAAG,MAAM,CAAC;IACZ,GAAG;IAEH,gBAAgB;IAChB,MAAM,eAAe,CAAC;QACpB,eAAe,EAAE,MAAM,CAAC,KAAK;QAC7B,eAAe,IAAI,qCAAqC;IAC1D;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,eAAe;QACjB;kCAAG;QAAC;QAAsB;KAAS;IAEnC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAG3C,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;0CAEvB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,kBAAkB,QAAQ,eAAe,UAAU;;;;;;kDAEtD,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAKjD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAG3C,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;0CAErB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,uBAAuB,QAAQ;;;;;;kDAElC,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAKjD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAG3C,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;0CAE3B,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,uBAAuB,QAAQ;;;;;;kDAElC,6LAAC;wCAAE,WAAU;;4CAAgC;4CAC1B;;;;;;;;;;;;;;;;;;;kCAIvB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAG3C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,uBAAuB,QAAQ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;kDAEjD,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU;oCACV,WAAU;;;;;;;;;;;0CAGd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,SAAS;wCACT,iBAAiB,CAAC,UAAY,wBAAwB,YAAY;;;;;;kDAEpE,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAe;;;;;;;;;;;;;;;;;;kCAGlC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAmB;;;;;;;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;sCACJ,6LAAC,oIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;kDACP,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;;;;;;;sCAGf,6LAAC,oIAAA,CAAA,YAAS;sCACP,uBACC,mBAAmB;4BACnB,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC,oIAAA,CAAA,WAAQ;;sDACP,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;mCApBT,CAAC,QAAQ,EAAE,OAAO;;;;4CAwBjC,qBAAqB,MAAM,GAAG,IAChC,qBAAqB,GAAG,CAAC,CAAC,2BACxB,6LAAC,oIAAA,CAAA,WAAQ;;sDACP,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,WAAW,qBAAqB;;;;;;sDAEnC,6LAAC,oIAAA,CAAA,YAAS;sDAAE,WAAW,cAAc;;;;;;sDACrC,6LAAC,oIAAA,CAAA,YAAS;sDAAE,WAAW,WAAW;;;;;;sDAClC,6LAAC,oIAAA,CAAA,YAAS;sDAAE,WAAW,KAAK,EAAE,UAAU;;;;;;sDACxC,6LAAC,oIAAA,CAAA,YAAS;sDACP,WAAW,KAAK,EAAE,OACjB,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,iBAAiB,IAAI,CAAC,GACjD,MACG;;;;;;sDAEP,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SACE,WAAW,aAAa,GAAG,YAAY;0DAGxC,WAAW,aAAa,GAAG,eAAe;;;;;;;;;;;sDAG/C,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,OAAO;0DAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,iBAAiB,EAAE,WAAW,qBAAqB,EAAE;8DAAE;;;;;;;;;;;;;;;;;mCA5B3D,WAAW,qBAAqB;;;;0DAoCjD,6LAAC,oIAAA,CAAA,WAAQ;0CACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oCAAC,SAAS;oCAAG,WAAU;8CAC9B,cACG,+CACA;;;;;;;;;;;;;;;;;;;;;;;;;;;YASf,aAAa,mBACZ,6LAAC,yIAAA,CAAA,aAAU;gBACT,aAAa;gBACb,YAAY;gBACZ,cAAc;;;;;;;;;;;;AAKxB;GA5TgB;;QAOa,kJAAA,CAAA,iBAAc;QAwBvC,wKAAA,CAAA,qCAAkC;QAMwB,0JAAA,CAAA,eAAY;;;KArC1D", "debugId": null}}, {"offset": {"line": 9416, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/inventory/api/inventory-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse } from \"@/types/api\";\r\nimport {\r\n  BranchInventory,\r\n  InventoryTransaction,\r\n  CreateInventoryTransactionRequest,\r\n  InventoryAdjustment,\r\n  CreateInventoryAdjustmentRequest,\r\n  UpdateInventoryAdjustmentStatusRequest,\r\n  InventoryReportSummary,\r\n  InventoryReportFilters,\r\n} from \"@/types/inventory\";\r\n\r\nexport const inventoryService = {\r\n  getBranchInventory: async (\r\n    branchId: number,\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<BranchInventory>> => {\r\n    try {\r\n      // Special case: branchId = -1 means \"All Branches\"\r\n      if (branchId === -1) {\r\n        console.log(\"Viewing all branches inventory\");\r\n        try {\r\n          const result = await inventoryService.getAllBranchesInventory(params);\r\n          console.log(\"All branches inventory result:\", result);\r\n          return result;\r\n        } catch (error) {\r\n          console.error(\"Error fetching all branches inventory:\", error);\r\n          throw error;\r\n        }\r\n      }\r\n\r\n      // If no branch ID is provided, try to get the user's branch ID\r\n      if (!branchId) {\r\n        try {\r\n          // Get the current user to determine their branch\r\n          const currentUser = await apiClient.get(\"/auth/me\");\r\n          console.log(\"Current user for inventory:\", currentUser);\r\n\r\n          // Type assertion to access branch_id\r\n          const userWithBranch = currentUser as any;\r\n          if (userWithBranch?.branch_id) {\r\n            console.log(\"Using user's branch_id:\", userWithBranch.branch_id);\r\n            branchId = userWithBranch.branch_id;\r\n          } else {\r\n            console.log(\"No branch ID available, returning empty data\");\r\n            return {\r\n              data: [],\r\n              pagination: {\r\n                total: 0,\r\n                page: 1,\r\n                limit: 0,\r\n                totalPages: 1,\r\n              },\r\n            };\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error getting user data:\", error);\r\n          return {\r\n            data: [],\r\n            pagination: {\r\n              total: 0,\r\n              page: 1,\r\n              limit: 0,\r\n              totalPages: 1,\r\n            },\r\n          };\r\n        }\r\n      }\r\n\r\n      // Use stock-items endpoint with branch_id for branch-specific inventory\r\n      const queryParams = {\r\n        ...params,\r\n        branch_id: branchId,\r\n      };\r\n\r\n      // Log search parameters for debugging\r\n      if (params?.search || params?.product_name || params?.['product.name']) {\r\n        console.log(`Searching inventory by product name:`, params?.product_name || params?.['product.name'] || params?.search);\r\n        console.log(`Note: This searches for stock items where the associated product name matches the search term`);\r\n      }\r\n\r\n      console.log(`Calling API: /stock-items with params:`, queryParams);\r\n      // Use the correct endpoint based on API structure\r\n      try {\r\n        const response = await apiClient.get(`/stock-items`, {\r\n          params: queryParams,\r\n        });\r\n        console.log(\"Raw API response:\", response);\r\n\r\n        // Check if response is undefined or null\r\n        if (!response) {\r\n          console.log(\"API response is undefined or null\");\r\n          return {\r\n            data: [],\r\n            pagination: {\r\n              total: 0,\r\n              page: 1,\r\n              limit: 0,\r\n              totalPages: 1,\r\n            },\r\n          };\r\n        }\r\n\r\n        // The API returns a paginated response with data and pagination properties\r\n        if (response && typeof response === \"object\" && \"data\" in response) {\r\n          console.log(\"Response is a paginated object with data property\");\r\n\r\n          // Extract the data array and pagination info\r\n          const { data, pagination } = response as {\r\n            data: BranchInventory[];\r\n            pagination: {\r\n              total: number;\r\n              page: number;\r\n              limit: number;\r\n              pages: number;\r\n            };\r\n          };\r\n\r\n          // Ensure we have valid pagination values\r\n          const total = pagination.total || 0;\r\n          const page = pagination.page || 1;\r\n          const limit = pagination.limit || 10;\r\n\r\n          // Calculate pages based on total and limit\r\n          const calculatedPages = Math.ceil(total / limit) || 1;\r\n\r\n          // Force at least 2 pages if we have more than 10 items total\r\n          const pages = total > 10 ? Math.max(2, calculatedPages) : calculatedPages;\r\n\r\n          console.log(\"Calculated pagination:\", {\r\n            apiTotal: pagination.total,\r\n            apiPage: pagination.page,\r\n            apiLimit: pagination.limit,\r\n            apiPages: pagination.pages,\r\n            calculatedPages,\r\n            forcedPages: pages,\r\n            shouldShowPagination: pages > 1\r\n          });\r\n\r\n          return {\r\n            data: data,\r\n            pagination: {\r\n              total: total,\r\n              page: page,\r\n              limit: limit,\r\n              pages: pages,\r\n            },\r\n          };\r\n        }\r\n        // Handle case where response is an array (unlikely with current API)\r\n        else if (Array.isArray(response)) {\r\n          console.log(\"Response is an array, converting to paginated format\");\r\n          const limit = params?.limit ? parseInt(params.limit as string, 10) : 10;\r\n          const page = params?.page ? parseInt(params.page as string, 10) : 1;\r\n          const total = response.length;\r\n\r\n          // Always calculate pages based on total and limit\r\n          const calculatedPages = Math.ceil(total / limit) || 1;\r\n\r\n          // Make sure we have at least 1 page\r\n          const pages = calculatedPages;\r\n\r\n          console.log(\"Pages calculation (fallback):\", {\r\n            calculatedPages,\r\n            finalPages: pages,\r\n            total,\r\n            limit,\r\n            shouldShowPagination: pages > 1\r\n          });\r\n\r\n          return {\r\n            data: response,\r\n            pagination: {\r\n              total: total,\r\n              page: page,\r\n              limit: limit,\r\n              pages: pages,\r\n            },\r\n          };\r\n        }\r\n        // If the response format is unexpected\r\n        else {\r\n          console.log(\"Unexpected response format:\", response);\r\n          return {\r\n            data: [],\r\n            pagination: {\r\n              total: 0,\r\n              page: 1,\r\n              limit: 10,\r\n              pages: 1,\r\n            },\r\n          };\r\n        }\r\n\r\n        // This code is unreachable due to the if-else above, but TypeScript doesn't know that\r\n        // We'll keep it for safety\r\n      } catch (error) {\r\n        console.error(\"Error in API call to stock-items:\", error);\r\n        throw error;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error in getBranchInventory:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 10,\r\n          pages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getProductInventory: async (\r\n    productId: number,\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<BranchInventory>> => {\r\n    return apiClient.get(`/products/${productId}/stock-items`, { params });\r\n  },\r\n\r\n  getInventoryTransactions: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<InventoryTransaction>> => {\r\n    return apiClient.get(\"/stock-movements\", { params });\r\n  },\r\n\r\n  getInventoryTransactionById: async (\r\n    id: number\r\n  ): Promise<InventoryTransaction> => {\r\n    return apiClient.get(`/stock-movements/${id}`);\r\n  },\r\n\r\n  createInventoryTransaction: async (\r\n    transaction: CreateInventoryTransactionRequest\r\n  ): Promise<InventoryTransaction> => {\r\n    return apiClient.post(\"/stock-movements\", transaction);\r\n  },\r\n\r\n  createStockItem: async (data: {\r\n    product_id: number;\r\n    branch_id: number;\r\n    quantity: number;\r\n    quantity_reserved?: number;\r\n    serial_numbers?: string[]; // Array of serial numbers for serialized products\r\n    buying_price?: number | string;\r\n    default_buying_price?: number | string;\r\n    default_selling_price?: number | string;\r\n    default_wholesale_price?: number | string;\r\n    buying_price_including_vat?: number | string;\r\n    buying_price_excluding_vat?: number | string;\r\n    buying_vat_amount?: number | string;\r\n    buying_vat_rate?: number | string;\r\n    batch_number?: string;\r\n    expiry_date?: string;\r\n    manufacturing_date?: string;\r\n    reorder_level?: number;\r\n    reorder_quantity?: number;\r\n    valuation_method?: 'FIFO' | 'LIFO' | 'WEIGHTED_AVERAGE';\r\n    tenant_id?: number;\r\n  }): Promise<any> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      // Required fields\r\n      product_id: data.product_id,\r\n      branch_id: data.branch_id,\r\n      quantity: data.quantity,\r\n\r\n      // Serial numbers for serialized products\r\n      serial_numbers: data.serial_numbers,\r\n\r\n      // Optional fields with backward compatibility\r\n      quantity_reserved: data.quantity_reserved,\r\n      buying_price: data.buying_price,\r\n      default_buying_price: data.default_buying_price || data.buying_price,\r\n      default_selling_price: data.default_selling_price,\r\n      default_wholesale_price: data.default_wholesale_price,\r\n\r\n      // VAT-related fields\r\n      buying_price_including_vat: data.buying_price_including_vat,\r\n      buying_price_excluding_vat: data.buying_price_excluding_vat,\r\n      buying_vat_amount: data.buying_vat_amount,\r\n      buying_vat_rate: data.buying_vat_rate,\r\n\r\n      // Batch/lot tracking\r\n      batch_number: data.batch_number,\r\n      expiry_date: data.expiry_date,\r\n      manufacturing_date: data.manufacturing_date,\r\n\r\n      // Inventory management\r\n      reorder_level: data.reorder_level,\r\n      reorder_quantity: data.reorder_quantity,\r\n      valuation_method: data.valuation_method,\r\n\r\n      // Tenant information\r\n      tenant_id: data.tenant_id,\r\n    };\r\n\r\n    console.log('Creating stock item with data:', apiRequest);\r\n\r\n    // For serialized products, we need to use a different endpoint\r\n    if (data.serial_numbers && data.serial_numbers.length > 0) {\r\n      console.log('Creating inventory items with serial numbers');\r\n      return apiClient.post(\"/inventory-items\", {\r\n        stock_item_id: null, // This will be created by the backend\r\n        product_id: data.product_id,\r\n        quantity: data.quantity,\r\n        serial_numbers: data.serial_numbers,\r\n        batch_number: data.batch_number,\r\n        expiry_date: data.expiry_date\r\n      });\r\n    }\r\n\r\n    // For non-serialized products, use the regular endpoint\r\n    return apiClient.post(\"/stock-items\", apiRequest);\r\n  },\r\n\r\n  updateStockItem: async (\r\n    id: number,\r\n    data: {\r\n      quantity?: number;\r\n      buying_price?: number | string;\r\n      selling_price?: number | string;\r\n      status?: string;\r\n      is_in_transit?: boolean;\r\n    }\r\n  ): Promise<any> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      quantity: data.quantity,\r\n      buying_price: data.buying_price,\r\n      selling_price: data.selling_price,\r\n      status: data.status,\r\n      is_in_transit: data.is_in_transit,\r\n    };\r\n\r\n    return apiClient.put(`/stock-items/${id}`, apiRequest);\r\n  },\r\n\r\n  updateStockItemStatus: async (\r\n    id: number,\r\n    status: string,\r\n    isInTransit: boolean = false\r\n  ): Promise<any> => {\r\n    // Update just the status of a stock item\r\n    const apiRequest = {\r\n      status: status,\r\n      is_in_transit: isInTransit,\r\n    };\r\n\r\n    return apiClient.put(`/stock-items/${id}/status`, apiRequest);\r\n  },\r\n\r\n  getInventoryAdjustments: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<InventoryAdjustment>> => {\r\n    return apiClient.get(\"/stock-adjustments\", { params });\r\n  },\r\n\r\n  getInventoryAdjustmentById: async (\r\n    id: number\r\n  ): Promise<InventoryAdjustment> => {\r\n    return apiClient.get(`/stock-adjustments/${id}`);\r\n  },\r\n\r\n  getStockAdjustmentTypes: async () => {\r\n    return apiClient.get(\"/stock-adjustments/types\");\r\n  },\r\n\r\n  getStockItemsByBranch: async (branch_id: number) => {\r\n    return apiClient.get(\"/stock-items\", {\r\n      params: { branch_id }\r\n    });\r\n  },\r\n\r\n  createInventoryAdjustment: async (\r\n    adjustment: CreateInventoryAdjustmentRequest\r\n  ): Promise<InventoryAdjustment> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      reference_number: `ADJ-${Date.now()}`,\r\n      location_id: adjustment.branch_id, // Map branch_id to location_id\r\n      adjustment_type_id: adjustment.adjustment_type_id, // Use the actual adjustment_type_id\r\n      status: \"pending\", // Default status for new adjustments\r\n      notes: adjustment.notes || \"\",\r\n      requested_by: 1, // Default to user ID 1, should be replaced with actual user ID from user context\r\n      items: adjustment.items.map((item) => ({\r\n        stock_item_id: item.stock_item_id, // Use the actual stock_item_id\r\n        quantity: item.quantity,\r\n        unit_cost: 0, // Default unit cost, should be replaced with actual unit cost if available\r\n        reason: item.reason, // Use the item-specific reason\r\n      })),\r\n    };\r\n\r\n    return apiClient.post(\"/stock-adjustments\", apiRequest);\r\n  },\r\n\r\n  updateInventoryAdjustmentStatus: async (\r\n    id: number,\r\n    status: UpdateInventoryAdjustmentStatusRequest\r\n  ): Promise<InventoryAdjustment> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      status: status.status, // 'approved', 'rejected', 'pending'\r\n      notes: status.notes || \"\",\r\n      processed_by: status.processed_by || 1, // Default to user ID 1, should be replaced with actual user ID from user context\r\n    };\r\n\r\n    return apiClient.put(`/stock-adjustments/${id}/process`, apiRequest);\r\n  },\r\n\r\n  getAllBranchesInventory: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<BranchInventory>> => {\r\n    try {\r\n      console.log(\"Fetching all branches first\");\r\n      // First, get all branches\r\n      const branchesResponse = await apiClient.get<any[]>(\"/branches\");\r\n      const branches = Array.isArray(branchesResponse) ? branchesResponse : [];\r\n\r\n      console.log(`Found ${branches.length} branches`);\r\n\r\n      if (branches.length === 0) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Fetch all inventory items at once without branch filter\r\n      console.log(\"Fetching all inventory items\");\r\n\r\n      // Log search parameters for debugging\r\n      if (params?.search || params?.product_name || params?.['product.name']) {\r\n        console.log(`Searching all branches inventory by product name:`, params?.product_name || params?.['product.name'] || params?.search);\r\n        console.log(`Note: This searches for stock items where the associated product name matches the search term`);\r\n      }\r\n\r\n      try {\r\n        // Pass search parameters to the API\r\n        const allInventoryResponse = await apiClient.get(`/stock-items`, {\r\n          params: {\r\n            // Include all possible search parameters to ensure compatibility with different backend implementations\r\n            search: params?.search,\r\n            product_name: params?.product_name,\r\n            'product.name': params?.['product.name'],\r\n            // Include other parameters like pagination\r\n            page: params?.page,\r\n            limit: params?.limit,\r\n          }\r\n        });\r\n\r\n        // Check if the response has the expected structure\r\n        if (\r\n          !allInventoryResponse ||\r\n          typeof allInventoryResponse !== \"object\" ||\r\n          !(\"data\" in allInventoryResponse)\r\n        ) {\r\n          console.log(\"Unexpected response format:\", allInventoryResponse);\r\n          return {\r\n            data: [],\r\n            pagination: {\r\n              total: 0,\r\n              page: 1,\r\n              limit: 0,\r\n              totalPages: 1,\r\n            },\r\n          };\r\n        }\r\n\r\n        // Extract the data array from the response\r\n        const inventoryItems = (allInventoryResponse as any).data || [];\r\n        console.log(`Received ${inventoryItems.length} inventory items`);\r\n\r\n        // Add branch information to each inventory item\r\n        const inventoryWithBranchInfo = inventoryItems.map((item: any) => {\r\n          // Find the branch for this item\r\n          const branch = branches.find((b) => b.id === item.branch_id);\r\n          return {\r\n            ...item,\r\n            Branch: branch || {\r\n              id: item.branch_id,\r\n              name: \"Unknown Branch\",\r\n              location: \"Unknown\",\r\n            },\r\n          };\r\n        });\r\n\r\n        // Apply pagination manually\r\n        const page = parseInt(params?.page || \"1\", 10);\r\n        const limit = parseInt(params?.limit || \"10\", 10);\r\n        const startIndex = (page - 1) * limit;\r\n        const endIndex = startIndex + limit;\r\n        const paginatedInventory = inventoryWithBranchInfo.slice(\r\n          startIndex,\r\n          endIndex\r\n        );\r\n\r\n        return {\r\n          data: paginatedInventory,\r\n          pagination: {\r\n            total: inventoryWithBranchInfo.length,\r\n            page,\r\n            limit,\r\n            pages: Math.ceil(inventoryWithBranchInfo.length / limit) || 1,\r\n          },\r\n        };\r\n      } catch (apiError) {\r\n        console.error(\"Error fetching inventory items:\", apiError);\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            pages: 1,\r\n          },\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching inventory for all branches:\", error);\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          pages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  // Helper function to process HQ inventory response\r\n  processHQInventoryResponse: (\r\n    response: any\r\n  ): PaginatedResponse<BranchInventory> => {\r\n    console.log(\"Processing HQ inventory response\");\r\n\r\n    // Process array response\r\n    if (Array.isArray(response)) {\r\n      console.log(\"Response is an array with\", response.length, \"items\");\r\n      const mappedItems = response.map((item: any) => {\r\n        // Make sure Product is properly handled\r\n        const product = item.Product || {};\r\n\r\n        return {\r\n          id: item.id,\r\n          branch_id: 0, // Use 0 to indicate HQ\r\n          product_id: item.product_id,\r\n          quantity: item.quantity,\r\n          buying_price: item.buying_price,\r\n          selling_price: item.selling_price,\r\n          // Add default price fields that the InventoryTable component looks for\r\n          default_selling_price: item.default_selling_price || item.selling_price,\r\n          default_buying_price: item.default_buying_price || item.buying_price,\r\n          default_wholesale_price: item.default_wholesale_price || item.wholesale_price,\r\n          created_at: item.created_at,\r\n          updated_at: item.updated_at,\r\n          deleted_at: item.deleted_at,\r\n          Branch: {\r\n            id: 0,\r\n            name: \"Headquarters\",\r\n            location: \"HQ\",\r\n          },\r\n          Product: product,\r\n          // Add reorder_level for consistency with branch inventory\r\n          reorder_level: product.reorder_level || 0,\r\n        };\r\n      });\r\n\r\n      console.log(\r\n        `Mapped ${mappedItems.length} HQ stock items to branch inventory format`\r\n      );\r\n\r\n      return {\r\n        data: mappedItems,\r\n        pagination: {\r\n          total: mappedItems.length,\r\n          page: 1,\r\n          limit: mappedItems.length,\r\n          pages: 1,\r\n        },\r\n      };\r\n    }\r\n\r\n    // Handle paginated response\r\n    if (response && typeof response === \"object\" && \"data\" in response) {\r\n      console.log(\"Response is a paginated object with data property\");\r\n      const { data, pagination } = response as any;\r\n\r\n      const mappedItems = data.map((item: any) => {\r\n        // Make sure Product is properly handled\r\n        const product = item.Product || {};\r\n\r\n        return {\r\n          id: item.id,\r\n          branch_id: 0, // Use 0 to indicate HQ\r\n          product_id: item.product_id,\r\n          quantity: item.quantity,\r\n          buying_price: item.buying_price,\r\n          selling_price: item.selling_price,\r\n          // Add default price fields that the InventoryTable component looks for\r\n          default_selling_price: item.default_selling_price || item.selling_price,\r\n          default_buying_price: item.default_buying_price || item.buying_price,\r\n          default_wholesale_price: item.default_wholesale_price || item.wholesale_price,\r\n          created_at: item.created_at,\r\n          updated_at: item.updated_at,\r\n          deleted_at: item.deleted_at,\r\n          Branch: {\r\n            id: 0,\r\n            name: \"Headquarters\",\r\n            location: \"HQ\",\r\n          },\r\n          Product: product,\r\n          // Add reorder_level for consistency with branch inventory\r\n          reorder_level: product.reorder_level || 0,\r\n        };\r\n      });\r\n\r\n      console.log(\r\n        `Mapped ${mappedItems.length} HQ stock items from paginated response`\r\n      );\r\n\r\n      return {\r\n        data: mappedItems,\r\n        pagination: {\r\n          total: pagination.total,\r\n          page: pagination.page,\r\n          limit: pagination.limit,\r\n          pages: pagination.pages || 1,\r\n        },\r\n      };\r\n    }\r\n\r\n    // Default fallback\r\n    console.log(\"No HQ stock items found or unexpected response format\");\r\n    return {\r\n      data: [],\r\n      pagination: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 0,\r\n        pages: 1,\r\n      },\r\n    };\r\n  },\r\n\r\n  getHQInventory: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<BranchInventory>> => {\r\n    try {\r\n      console.log(\"Fetching HQ inventory with params:\", params);\r\n\r\n      // Use stock-items endpoint with branch_id=1 for headquarters stock\r\n      const queryParams = {\r\n        ...params,\r\n        branch_id: 1, // Use branch_id=1 for headquarters stock\r\n      };\r\n\r\n      // Log search parameters for debugging\r\n      if (params?.search || params?.product_name || params?.['product.name']) {\r\n        console.log(`Searching HQ inventory by product name:`, params?.product_name || params?.['product.name'] || params?.search);\r\n        console.log(`Note: This searches for stock items where the associated product name matches the search term`);\r\n      }\r\n\r\n      console.log(\"Calling API: stock-items with params:\", queryParams);\r\n\r\n      const response = await apiClient.get(\"/stock-items\", {\r\n        params: queryParams,\r\n      });\r\n      console.log(\"Stock items API call successful\");\r\n\r\n      // Process the response\r\n      return inventoryService.processHQInventoryResponse(response);\r\n    } catch (error) {\r\n      console.error(\"Error fetching HQ inventory:\", error);\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          pages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getInventoryReportSummary: async (\r\n    filters?: InventoryReportFilters\r\n  ): Promise<InventoryReportSummary> => {\r\n    try {\r\n      console.log(\r\n        \"Calling API: /reports/inventory/summary with filters:\",\r\n        filters\r\n      );\r\n      const response = await apiClient.get<any>(\"/reports/inventory/summary\", {\r\n        params: filters,\r\n      });\r\n      console.log(\r\n        \"Raw inventory report API response:\",\r\n        JSON.stringify(response, null, 2)\r\n      );\r\n\r\n      // Return the response directly\r\n      return response as InventoryReportSummary;\r\n    } catch (error) {\r\n      console.error(\"Error in getInventoryReportSummary:\", error);\r\n      // Return default data structure on error\r\n      return {\r\n        total_products: 0,\r\n        total_value: 0,\r\n        low_stock_count: 0,\r\n        out_of_stock_count: 0,\r\n        top_products: [],\r\n        by_category: [],\r\n        by_branch: [],\r\n      };\r\n    }\r\n  },\r\n\r\n  getStockValuationReport: async (filters?: {\r\n    branch_id?: number;\r\n    category_id?: number;\r\n    valuation_method?: 'FIFO' | 'LIFO' | 'WEIGHTED_AVERAGE';\r\n    include_zero_stock?: boolean;\r\n  }) => {\r\n    try {\r\n      console.log(\r\n        \"Calling API: /reports/inventory/valuation with filters:\",\r\n        filters\r\n      );\r\n      const response = await apiClient.get<any>(\"/reports/inventory/valuation\", {\r\n        params: filters,\r\n      });\r\n      console.log(\r\n        \"Raw stock valuation report API response:\",\r\n        JSON.stringify(response, null, 2)\r\n      );\r\n\r\n      // Return the response directly\r\n      return response;\r\n    } catch (error) {\r\n      console.error(\"Error in getStockValuationReport:\", error);\r\n      // Return default data structure on error\r\n      return {\r\n        summary: {\r\n          total_items: 0,\r\n          total_quantity: 0,\r\n          total_value: 0,\r\n          valuation_methods: {\r\n            FIFO: { count: 0, value: 0 },\r\n            LIFO: { count: 0, value: 0 },\r\n            WEIGHTED_AVERAGE: { count: 0, value: 0 }\r\n          }\r\n        },\r\n        items: [],\r\n        by_category: [],\r\n        by_branch: [],\r\n        by_valuation_method: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get all branches\r\n  getBranches: async () => {\r\n    try {\r\n      console.log(\"Fetching all branches\");\r\n      const response = await apiClient.get(\"/branches\");\r\n\r\n      // Check if the response is an array (direct branches list)\r\n      if (Array.isArray(response)) {\r\n        console.log(`Received ${response.length} branches directly`);\r\n        return {\r\n          data: response,\r\n          total: response.length\r\n        };\r\n      }\r\n\r\n      // Check if the response has a data property (paginated response)\r\n      if (response && typeof response === \"object\" && \"data\" in response) {\r\n        console.log(`Received ${(response as any).data.length} branches in paginated format`);\r\n        const { data, pagination } = response as any;\r\n        return {\r\n          data,\r\n          total: pagination?.total || data.length\r\n        };\r\n      }\r\n\r\n      // Default fallback\r\n      console.log(\"Unexpected branches response format, returning empty array\");\r\n      return {\r\n        data: [],\r\n        total: 0\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching branches:\", error);\r\n      return {\r\n        data: [],\r\n        total: 0\r\n      };\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAaO,MAAM,mBAAmB;IAC9B,oBAAoB,OAClB,UACA;QAEA,IAAI;YACF,mDAAmD;YACnD,IAAI,aAAa,CAAC,GAAG;gBACnB,QAAQ,GAAG,CAAC;gBACZ,IAAI;oBACF,MAAM,SAAS,MAAM,iBAAiB,uBAAuB,CAAC;oBAC9D,QAAQ,GAAG,CAAC,kCAAkC;oBAC9C,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0CAA0C;oBACxD,MAAM;gBACR;YACF;YAEA,+DAA+D;YAC/D,IAAI,CAAC,UAAU;gBACb,IAAI;oBACF,iDAAiD;oBACjD,MAAM,cAAc,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;oBACxC,QAAQ,GAAG,CAAC,+BAA+B;oBAE3C,qCAAqC;oBACrC,MAAM,iBAAiB;oBACvB,IAAI,gBAAgB,WAAW;wBAC7B,QAAQ,GAAG,CAAC,2BAA2B,eAAe,SAAS;wBAC/D,WAAW,eAAe,SAAS;oBACrC,OAAO;wBACL,QAAQ,GAAG,CAAC;wBACZ,OAAO;4BACL,MAAM,EAAE;4BACR,YAAY;gCACV,OAAO;gCACP,MAAM;gCACN,OAAO;gCACP,YAAY;4BACd;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC1C,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;oBACF;gBACF;YACF;YAEA,wEAAwE;YACxE,MAAM,cAAc;gBAClB,GAAG,MAAM;gBACT,WAAW;YACb;YAEA,sCAAsC;YACtC,IAAI,QAAQ,UAAU,QAAQ,gBAAgB,QAAQ,CAAC,eAAe,EAAE;gBACtE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,CAAC,EAAE,QAAQ,gBAAgB,QAAQ,CAAC,eAAe,IAAI,QAAQ;gBAChH,QAAQ,GAAG,CAAC,CAAC,6FAA6F,CAAC;YAC7G;YAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC,EAAE;YACtD,kDAAkD;YAClD,IAAI;gBACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,EAAE;oBACnD,QAAQ;gBACV;gBACA,QAAQ,GAAG,CAAC,qBAAqB;gBAEjC,yCAAyC;gBACzC,IAAI,CAAC,UAAU;oBACb,QAAQ,GAAG,CAAC;oBACZ,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;oBACF;gBACF;gBAEA,2EAA2E;gBAC3E,IAAI,YAAY,OAAO,aAAa,YAAY,UAAU,UAAU;oBAClE,QAAQ,GAAG,CAAC;oBAEZ,6CAA6C;oBAC7C,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG;oBAU7B,yCAAyC;oBACzC,MAAM,QAAQ,WAAW,KAAK,IAAI;oBAClC,MAAM,OAAO,WAAW,IAAI,IAAI;oBAChC,MAAM,QAAQ,WAAW,KAAK,IAAI;oBAElC,2CAA2C;oBAC3C,MAAM,kBAAkB,KAAK,IAAI,CAAC,QAAQ,UAAU;oBAEpD,6DAA6D;oBAC7D,MAAM,QAAQ,QAAQ,KAAK,KAAK,GAAG,CAAC,GAAG,mBAAmB;oBAE1D,QAAQ,GAAG,CAAC,0BAA0B;wBACpC,UAAU,WAAW,KAAK;wBAC1B,SAAS,WAAW,IAAI;wBACxB,UAAU,WAAW,KAAK;wBAC1B,UAAU,WAAW,KAAK;wBAC1B;wBACA,aAAa;wBACb,sBAAsB,QAAQ;oBAChC;oBAEA,OAAO;wBACL,MAAM;wBACN,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,OAAO;wBACT;oBACF;gBACF,OAEK,IAAI,MAAM,OAAO,CAAC,WAAW;oBAChC,QAAQ,GAAG,CAAC;oBACZ,MAAM,QAAQ,QAAQ,QAAQ,SAAS,OAAO,KAAK,EAAY,MAAM;oBACrE,MAAM,OAAO,QAAQ,OAAO,SAAS,OAAO,IAAI,EAAY,MAAM;oBAClE,MAAM,QAAQ,SAAS,MAAM;oBAE7B,kDAAkD;oBAClD,MAAM,kBAAkB,KAAK,IAAI,CAAC,QAAQ,UAAU;oBAEpD,oCAAoC;oBACpC,MAAM,QAAQ;oBAEd,QAAQ,GAAG,CAAC,iCAAiC;wBAC3C;wBACA,YAAY;wBACZ;wBACA;wBACA,sBAAsB,QAAQ;oBAChC;oBAEA,OAAO;wBACL,MAAM;wBACN,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,OAAO;wBACT;oBACF;gBACF,OAEK;oBACH,QAAQ,GAAG,CAAC,+BAA+B;oBAC3C,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,OAAO;wBACT;oBACF;gBACF;YAEA,sFAAsF;YACtF,2BAA2B;YAC7B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,OAAO;gBACT;YACF;QACF;IACF;IAEA,qBAAqB,OACnB,WACA;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,YAAY,CAAC,EAAE;YAAE;QAAO;IACtE;IAEA,0BAA0B,OACxB;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,oBAAoB;YAAE;QAAO;IACpD;IAEA,6BAA6B,OAC3B;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,IAAI;IAC/C;IAEA,4BAA4B,OAC1B;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,oBAAoB;IAC5C;IAEA,iBAAiB,OAAO;QAsBtB,sDAAsD;QACtD,MAAM,aAAa;YACjB,kBAAkB;YAClB,YAAY,KAAK,UAAU;YAC3B,WAAW,KAAK,SAAS;YACzB,UAAU,KAAK,QAAQ;YAEvB,yCAAyC;YACzC,gBAAgB,KAAK,cAAc;YAEnC,8CAA8C;YAC9C,mBAAmB,KAAK,iBAAiB;YACzC,cAAc,KAAK,YAAY;YAC/B,sBAAsB,KAAK,oBAAoB,IAAI,KAAK,YAAY;YACpE,uBAAuB,KAAK,qBAAqB;YACjD,yBAAyB,KAAK,uBAAuB;YAErD,qBAAqB;YACrB,4BAA4B,KAAK,0BAA0B;YAC3D,4BAA4B,KAAK,0BAA0B;YAC3D,mBAAmB,KAAK,iBAAiB;YACzC,iBAAiB,KAAK,eAAe;YAErC,qBAAqB;YACrB,cAAc,KAAK,YAAY;YAC/B,aAAa,KAAK,WAAW;YAC7B,oBAAoB,KAAK,kBAAkB;YAE3C,uBAAuB;YACvB,eAAe,KAAK,aAAa;YACjC,kBAAkB,KAAK,gBAAgB;YACvC,kBAAkB,KAAK,gBAAgB;YAEvC,qBAAqB;YACrB,WAAW,KAAK,SAAS;QAC3B;QAEA,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,+DAA+D;QAC/D,IAAI,KAAK,cAAc,IAAI,KAAK,cAAc,CAAC,MAAM,GAAG,GAAG;YACzD,QAAQ,GAAG,CAAC;YACZ,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,oBAAoB;gBACxC,eAAe;gBACf,YAAY,KAAK,UAAU;gBAC3B,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,cAAc;gBACnC,cAAc,KAAK,YAAY;gBAC/B,aAAa,KAAK,WAAW;YAC/B;QACF;QAEA,wDAAwD;QACxD,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBAAgB;IACxC;IAEA,iBAAiB,OACf,IACA;QAQA,sDAAsD;QACtD,MAAM,aAAa;YACjB,UAAU,KAAK,QAAQ;YACvB,cAAc,KAAK,YAAY;YAC/B,eAAe,KAAK,aAAa;YACjC,QAAQ,KAAK,MAAM;YACnB,eAAe,KAAK,aAAa;QACnC;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI,EAAE;IAC7C;IAEA,uBAAuB,OACrB,IACA,QACA,cAAuB,KAAK;QAE5B,yCAAyC;QACzC,MAAM,aAAa;YACjB,QAAQ;YACR,eAAe;QACjB;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;IACpD;IAEA,yBAAyB,OACvB;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,sBAAsB;YAAE;QAAO;IACtD;IAEA,4BAA4B,OAC1B;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,IAAI;IACjD;IAEA,yBAAyB;QACvB,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;IACvB;IAEA,uBAAuB,OAAO;QAC5B,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,gBAAgB;YACnC,QAAQ;gBAAE;YAAU;QACtB;IACF;IAEA,2BAA2B,OACzB;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,kBAAkB,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;YACrC,aAAa,WAAW,SAAS;YACjC,oBAAoB,WAAW,kBAAkB;YACjD,QAAQ;YACR,OAAO,WAAW,KAAK,IAAI;YAC3B,cAAc;YACd,OAAO,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;oBACrC,eAAe,KAAK,aAAa;oBACjC,UAAU,KAAK,QAAQ;oBACvB,WAAW;oBACX,QAAQ,KAAK,MAAM;gBACrB,CAAC;QACH;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,sBAAsB;IAC9C;IAEA,iCAAiC,OAC/B,IACA;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,QAAQ,OAAO,MAAM;YACrB,OAAO,OAAO,KAAK,IAAI;YACvB,cAAc,OAAO,YAAY,IAAI;QACvC;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,GAAG,QAAQ,CAAC,EAAE;IAC3D;IAEA,yBAAyB,OACvB;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,0BAA0B;YAC1B,MAAM,mBAAmB,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAQ;YACpD,MAAM,WAAW,MAAM,OAAO,CAAC,oBAAoB,mBAAmB,EAAE;YAExE,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;YAE/C,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,OAAO;oBACL,MAAM,EAAE;oBACR,YAAY;wBACV,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YAEA,0DAA0D;YAC1D,QAAQ,GAAG,CAAC;YAEZ,sCAAsC;YACtC,IAAI,QAAQ,UAAU,QAAQ,gBAAgB,QAAQ,CAAC,eAAe,EAAE;gBACtE,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC,EAAE,QAAQ,gBAAgB,QAAQ,CAAC,eAAe,IAAI,QAAQ;gBAC7H,QAAQ,GAAG,CAAC,CAAC,6FAA6F,CAAC;YAC7G;YAEA,IAAI;gBACF,oCAAoC;gBACpC,MAAM,uBAAuB,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,EAAE;oBAC/D,QAAQ;wBACN,wGAAwG;wBACxG,QAAQ,QAAQ;wBAChB,cAAc,QAAQ;wBACtB,gBAAgB,QAAQ,CAAC,eAAe;wBACxC,2CAA2C;wBAC3C,MAAM,QAAQ;wBACd,OAAO,QAAQ;oBACjB;gBACF;gBAEA,mDAAmD;gBACnD,IACE,CAAC,wBACD,OAAO,yBAAyB,YAChC,CAAC,CAAC,UAAU,oBAAoB,GAChC;oBACA,QAAQ,GAAG,CAAC,+BAA+B;oBAC3C,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;oBACF;gBACF;gBAEA,2CAA2C;gBAC3C,MAAM,iBAAiB,AAAC,qBAA6B,IAAI,IAAI,EAAE;gBAC/D,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,eAAe,MAAM,CAAC,gBAAgB,CAAC;gBAE/D,gDAAgD;gBAChD,MAAM,0BAA0B,eAAe,GAAG,CAAC,CAAC;oBAClD,gCAAgC;oBAChC,MAAM,SAAS,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,SAAS;oBAC3D,OAAO;wBACL,GAAG,IAAI;wBACP,QAAQ,UAAU;4BAChB,IAAI,KAAK,SAAS;4BAClB,MAAM;4BACN,UAAU;wBACZ;oBACF;gBACF;gBAEA,4BAA4B;gBAC5B,MAAM,OAAO,SAAS,QAAQ,QAAQ,KAAK;gBAC3C,MAAM,QAAQ,SAAS,QAAQ,SAAS,MAAM;gBAC9C,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;gBAChC,MAAM,WAAW,aAAa;gBAC9B,MAAM,qBAAqB,wBAAwB,KAAK,CACtD,YACA;gBAGF,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,wBAAwB,MAAM;wBACrC;wBACA;wBACA,OAAO,KAAK,IAAI,CAAC,wBAAwB,MAAM,GAAG,UAAU;oBAC9D;gBACF;YACF,EAAE,OAAO,UAAU;gBACjB,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,OAAO;oBACL,MAAM,EAAE;oBACR,YAAY;wBACV,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,OAAO;oBACT;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,OAAO;gBACT;YACF;QACF;IACF;IAEA,mDAAmD;IACnD,4BAA4B,CAC1B;QAEA,QAAQ,GAAG,CAAC;QAEZ,yBAAyB;QACzB,IAAI,MAAM,OAAO,CAAC,WAAW;YAC3B,QAAQ,GAAG,CAAC,6BAA6B,SAAS,MAAM,EAAE;YAC1D,MAAM,cAAc,SAAS,GAAG,CAAC,CAAC;gBAChC,wCAAwC;gBACxC,MAAM,UAAU,KAAK,OAAO,IAAI,CAAC;gBAEjC,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,WAAW;oBACX,YAAY,KAAK,UAAU;oBAC3B,UAAU,KAAK,QAAQ;oBACvB,cAAc,KAAK,YAAY;oBAC/B,eAAe,KAAK,aAAa;oBACjC,uEAAuE;oBACvE,uBAAuB,KAAK,qBAAqB,IAAI,KAAK,aAAa;oBACvE,sBAAsB,KAAK,oBAAoB,IAAI,KAAK,YAAY;oBACpE,yBAAyB,KAAK,uBAAuB,IAAI,KAAK,eAAe;oBAC7E,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;oBAC3B,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,UAAU;oBACZ;oBACA,SAAS;oBACT,0DAA0D;oBAC1D,eAAe,QAAQ,aAAa,IAAI;gBAC1C;YACF;YAEA,QAAQ,GAAG,CACT,CAAC,OAAO,EAAE,YAAY,MAAM,CAAC,0CAA0C,CAAC;YAG1E,OAAO;gBACL,MAAM;gBACN,YAAY;oBACV,OAAO,YAAY,MAAM;oBACzB,MAAM;oBACN,OAAO,YAAY,MAAM;oBACzB,OAAO;gBACT;YACF;QACF;QAEA,4BAA4B;QAC5B,IAAI,YAAY,OAAO,aAAa,YAAY,UAAU,UAAU;YAClE,QAAQ,GAAG,CAAC;YACZ,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG;YAE7B,MAAM,cAAc,KAAK,GAAG,CAAC,CAAC;gBAC5B,wCAAwC;gBACxC,MAAM,UAAU,KAAK,OAAO,IAAI,CAAC;gBAEjC,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,WAAW;oBACX,YAAY,KAAK,UAAU;oBAC3B,UAAU,KAAK,QAAQ;oBACvB,cAAc,KAAK,YAAY;oBAC/B,eAAe,KAAK,aAAa;oBACjC,uEAAuE;oBACvE,uBAAuB,KAAK,qBAAqB,IAAI,KAAK,aAAa;oBACvE,sBAAsB,KAAK,oBAAoB,IAAI,KAAK,YAAY;oBACpE,yBAAyB,KAAK,uBAAuB,IAAI,KAAK,eAAe;oBAC7E,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;oBAC3B,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,UAAU;oBACZ;oBACA,SAAS;oBACT,0DAA0D;oBAC1D,eAAe,QAAQ,aAAa,IAAI;gBAC1C;YACF;YAEA,QAAQ,GAAG,CACT,CAAC,OAAO,EAAE,YAAY,MAAM,CAAC,uCAAuC,CAAC;YAGvE,OAAO;gBACL,MAAM;gBACN,YAAY;oBACV,OAAO,WAAW,KAAK;oBACvB,MAAM,WAAW,IAAI;oBACrB,OAAO,WAAW,KAAK;oBACvB,OAAO,WAAW,KAAK,IAAI;gBAC7B;YACF;QACF;QAEA,mBAAmB;QACnB,QAAQ,GAAG,CAAC;QACZ,OAAO;YACL,MAAM,EAAE;YACR,YAAY;gBACV,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;QACF;IACF;IAEA,gBAAgB,OACd;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,sCAAsC;YAElD,mEAAmE;YACnE,MAAM,cAAc;gBAClB,GAAG,MAAM;gBACT,WAAW;YACb;YAEA,sCAAsC;YACtC,IAAI,QAAQ,UAAU,QAAQ,gBAAgB,QAAQ,CAAC,eAAe,EAAE;gBACtE,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC,EAAE,QAAQ,gBAAgB,QAAQ,CAAC,eAAe,IAAI,QAAQ;gBACnH,QAAQ,GAAG,CAAC,CAAC,6FAA6F,CAAC;YAC7G;YAEA,QAAQ,GAAG,CAAC,yCAAyC;YAErD,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,gBAAgB;gBACnD,QAAQ;YACV;YACA,QAAQ,GAAG,CAAC;YAEZ,uBAAuB;YACvB,OAAO,iBAAiB,0BAA0B,CAAC;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,OAAO;gBACT;YACF;QACF;IACF;IAEA,2BAA2B,OACzB;QAEA,IAAI;YACF,QAAQ,GAAG,CACT,yDACA;YAEF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,8BAA8B;gBACtE,QAAQ;YACV;YACA,QAAQ,GAAG,CACT,sCACA,KAAK,SAAS,CAAC,UAAU,MAAM;YAGjC,+BAA+B;YAC/B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,yCAAyC;YACzC,OAAO;gBACL,gBAAgB;gBAChB,aAAa;gBACb,iBAAiB;gBACjB,oBAAoB;gBACpB,cAAc,EAAE;gBAChB,aAAa,EAAE;gBACf,WAAW,EAAE;YACf;QACF;IACF;IAEA,yBAAyB,OAAO;QAM9B,IAAI;YACF,QAAQ,GAAG,CACT,2DACA;YAEF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,gCAAgC;gBACxE,QAAQ;YACV;YACA,QAAQ,GAAG,CACT,4CACA,KAAK,SAAS,CAAC,UAAU,MAAM;YAGjC,+BAA+B;YAC/B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,yCAAyC;YACzC,OAAO;gBACL,SAAS;oBACP,aAAa;oBACb,gBAAgB;oBAChB,aAAa;oBACb,mBAAmB;wBACjB,MAAM;4BAAE,OAAO;4BAAG,OAAO;wBAAE;wBAC3B,MAAM;4BAAE,OAAO;4BAAG,OAAO;wBAAE;wBAC3B,kBAAkB;4BAAE,OAAO;4BAAG,OAAO;wBAAE;oBACzC;gBACF;gBACA,OAAO,EAAE;gBACT,aAAa,EAAE;gBACf,WAAW,EAAE;gBACb,qBAAqB,EAAE;YACzB;QACF;IACF;IAEA,mBAAmB;IACnB,aAAa;QACX,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;YAErC,2DAA2D;YAC3D,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,MAAM,CAAC,kBAAkB,CAAC;gBAC3D,OAAO;oBACL,MAAM;oBACN,OAAO,SAAS,MAAM;gBACxB;YACF;YAEA,iEAAiE;YACjE,IAAI,YAAY,OAAO,aAAa,YAAY,UAAU,UAAU;gBAClE,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,AAAC,SAAiB,IAAI,CAAC,MAAM,CAAC,6BAA6B,CAAC;gBACpF,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG;gBAC7B,OAAO;oBACL;oBACA,OAAO,YAAY,SAAS,KAAK,MAAM;gBACzC;YACF;YAEA,mBAAmB;YACnB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,MAAM,EAAE;gBACR,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,MAAM,EAAE;gBACR,OAAO;YACT;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 10063, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/inventory/hooks/use-inventory.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { inventoryService } from \"../api/inventory-service\";\r\nimport {\r\n  CreateInventoryTransactionRequest,\r\n  CreateInventoryAdjustmentRequest,\r\n  UpdateInventoryAdjustmentStatusRequest,\r\n  InventoryReportFilters,\r\n  StockAdjustmentType,\r\n  StockItem,\r\n} from \"@/types/inventory\";\r\nimport {\r\n  InventoryReportSummary,\r\n  StockValuationReport,\r\n  StockValuationReportFilters\r\n} from \"../types\";\r\nimport { toast } from \"sonner\";\r\n\r\n// Hook for HQ inventory (for admin users)\r\nexport function useHQInventory(\r\n  params?: Record<string, any>,\r\n  isEnabled?: boolean\r\n) {\r\n  return useQuery({\r\n    queryKey: [\"hqInventory\", params],\r\n    queryFn: async () => {\r\n      try {\r\n        // Extract pagination parameters\r\n        const page = params?.page || 1;\r\n        const limit = params?.limit || 10;\r\n\r\n        const result = await inventoryService.getHQInventory(params);\r\n\r\n        // Ensure pagination data is properly structured\r\n        if (!result.pagination) {\r\n          result.pagination = {\r\n            total: result.data.length,\r\n            page: parseInt(page as string, 10),\r\n            limit: parseInt(limit as string, 10),\r\n            pages: Math.ceil(result.data.length / parseInt(limit as string, 10))\r\n          };\r\n        } else {\r\n          // Always calculate pages based on total and limit\r\n          const calculatedPages = Math.ceil(result.pagination.total / parseInt(limit as string, 10)) || 1;\r\n\r\n          // Force at least 2 pages if we have more than 10 items total\r\n          const forcedPages = result.pagination.total > 10 ? Math.max(2, calculatedPages) : calculatedPages;\r\n\r\n          console.log(`Calculated pagination:`, {\r\n            apiPages: result.pagination.pages,\r\n            calculatedPages,\r\n            forcedPages,\r\n            total: result.pagination.total,\r\n            limit\r\n          });\r\n\r\n          // Always use our calculated pages value\r\n          result.pagination.pages = forcedPages;\r\n\r\n          // Ensure the page value matches what was requested\r\n          if (result.pagination.page !== parseInt(page as string, 10)) {\r\n            console.log(`Fixing page: ${result.pagination.page} -> ${page}`);\r\n            result.pagination.page = parseInt(page as string, 10);\r\n          }\r\n        }\r\n\r\n        console.log(\"HQ Inventory API response:\", result);\r\n        console.log(\"Pagination data:\", result.pagination);\r\n        return result;\r\n      } catch (error) {\r\n        console.error(\"Error fetching HQ inventory:\", error);\r\n        throw error;\r\n      }\r\n    },\r\n    enabled: isEnabled !== undefined ? isEnabled : true,\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            pages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\n// Hook for branch-specific inventory\r\nexport function useBranchInventory(\r\n  branchId: number,\r\n  params?: Record<string, any>,\r\n  isEnabled?: boolean\r\n) {\r\n  console.log(\"useBranchInventory called with:\", {\r\n    branchId,\r\n    params,\r\n    isEnabled,\r\n  });\r\n\r\n  return useQuery({\r\n    queryKey: [\"branchInventory\", branchId, params],\r\n    // Log when the query is triggered\r\n    onFetch: () => {\r\n      console.log(\"Branch inventory query triggered:\", {\r\n        branchId,\r\n        params\r\n      });\r\n    },\r\n    queryFn: async () => {\r\n      try {\r\n        console.log(\"Fetching inventory for branch:\", branchId);\r\n        console.log(\"With params:\", params);\r\n\r\n        // This hook is now only for specific branches, not HQ\r\n        if (branchId === -1) {\r\n          console.log(\"This is a request for ALL branches inventory\");\r\n        } else {\r\n          console.log(`This is a request for branch ${branchId} inventory`);\r\n        }\r\n\r\n        // Extract pagination parameters\r\n        const page = params?.page || 1;\r\n        const limit = params?.limit || 10;\r\n        console.log(`Pagination: page=${page}, limit=${limit}`);\r\n\r\n        const result = await inventoryService.getBranchInventory(\r\n          branchId,\r\n          params\r\n        );\r\n\r\n        // Ensure pagination data is properly structured\r\n        if (!result.pagination) {\r\n          console.log(\"Adding pagination data to result\");\r\n          result.pagination = {\r\n            total: result.data.length,\r\n            page: parseInt(page as string, 10),\r\n            limit: parseInt(limit as string, 10),\r\n            pages: Math.ceil(result.data.length / parseInt(limit as string, 10))\r\n          };\r\n        } else {\r\n          // Make sure we're using the correct page from the API response\r\n          console.log(`API returned page: ${result.pagination.page}, requested page: ${page}`);\r\n\r\n          // Always calculate pages based on total and limit\r\n          const calculatedPages = Math.ceil(result.pagination.total / parseInt(limit as string, 10)) || 1;\r\n\r\n          // Force at least 2 pages if we have more than 10 items total\r\n          const forcedPages = result.pagination.total > 10 ? Math.max(2, calculatedPages) : calculatedPages;\r\n\r\n          console.log(`Calculated pagination:`, {\r\n            apiPages: result.pagination.pages,\r\n            calculatedPages,\r\n            forcedPages,\r\n            total: result.pagination.total,\r\n            limit\r\n          });\r\n\r\n          // Always use our calculated pages value\r\n          result.pagination.pages = forcedPages;\r\n\r\n          // Ensure the page value matches what was requested\r\n          if (result.pagination.page !== parseInt(page as string, 10)) {\r\n            console.log(`Fixing page: ${result.pagination.page} -> ${page}`);\r\n            result.pagination.page = parseInt(page as string, 10);\r\n          }\r\n\r\n          // For backward compatibility, set totalPages to pages\r\n          result.pagination.totalPages = result.pagination.pages;\r\n        }\r\n\r\n        console.log(\"Branch Inventory API response:\", result);\r\n        console.log(\"Pagination data:\", result.pagination);\r\n        return result;\r\n      } catch (error) {\r\n        console.error(\"Error fetching branch inventory:\", error);\r\n        throw error;\r\n      }\r\n    },\r\n    enabled: (() => {\r\n      // Always enable the query if isEnabled is explicitly set\r\n      if (isEnabled !== undefined) return isEnabled;\r\n\r\n      // Otherwise, enable for any valid branchId (including -1 for All Branches)\r\n      return branchId !== undefined;\r\n    })(),\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            pages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useProductInventory(\r\n  productId: number,\r\n  params?: Record<string, any>\r\n) {\r\n  return useQuery({\r\n    queryKey: [\"productInventory\", productId, params],\r\n    queryFn: async () => {\r\n      if (!productId) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            pages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      try {\r\n        const result = await inventoryService.getProductInventory(\r\n          productId,\r\n          params\r\n        );\r\n        return result;\r\n      } catch (error) {\r\n        console.error(\"Error fetching product inventory:\", error);\r\n        throw error;\r\n      }\r\n    },\r\n    enabled: !!productId,\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            pages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useInventoryTransactions(params?: Record<string, any>) {\r\n  return useQuery({\r\n    queryKey: [\"inventoryTransactions\", params],\r\n    queryFn: async () => {\r\n      try {\r\n        const result = await inventoryService.getInventoryTransactions(params);\r\n        return result;\r\n      } catch (error) {\r\n        console.error(\"Error fetching inventory transactions:\", error);\r\n        throw error;\r\n      }\r\n    },\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            pages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useInventoryTransaction(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"inventoryTransaction\", id],\r\n    queryFn: () => inventoryService.getInventoryTransactionById(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateInventoryTransaction() {\r\n  const queryClient = useQueryClient();\r\n  return useMutation({\r\n    mutationFn: (transaction: CreateInventoryTransactionRequest) =>\r\n      inventoryService.createInventoryTransaction(transaction),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"inventoryTransactions\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branchInventory\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"productInventory\"] });\r\n      toast.success(\"Transaction created\", {\r\n        description: \"The inventory transaction has been created successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error creating transaction\", {\r\n        description:\r\n          error.message || \"An error occurred while creating the transaction.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useInventoryAdjustments(params?: Record<string, any>) {\r\n  return useQuery({\r\n    queryKey: [\"inventoryAdjustments\", params],\r\n    queryFn: () => inventoryService.getInventoryAdjustments(params),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n  });\r\n}\r\n\r\nexport function useInventoryAdjustment(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"inventoryAdjustment\", id],\r\n    queryFn: () => inventoryService.getInventoryAdjustmentById(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateInventoryAdjustment() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (adjustment: CreateInventoryAdjustmentRequest) =>\r\n      inventoryService.createInventoryAdjustment(adjustment),\r\n    onSuccess: (data, variables) => {\r\n      // Invalidate all inventory-related queries to refresh data\r\n      queryClient.invalidateQueries({ queryKey: [\"inventoryAdjustments\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"inventoryTransactions\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branchInventory\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"hqInventory\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"productInventory\"] });\r\n\r\n      // Specifically invalidate stock items for the branch that was adjusted\r\n      if (variables.branch_id) {\r\n        queryClient.invalidateQueries({ queryKey: [\"stockItems\", variables.branch_id] });\r\n      }\r\n\r\n      toast.success(\"Adjustment created\", {\r\n        description: \"The inventory adjustment has been created successfully and inventory has been updated.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error creating adjustment\", {\r\n        description:\r\n          error.message || \"An error occurred while creating the adjustment.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateInventoryAdjustmentStatus(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (status: UpdateInventoryAdjustmentStatusRequest) =>\r\n      inventoryService.updateInventoryAdjustmentStatus(id, status),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"inventoryAdjustments\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"inventoryAdjustment\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branchInventory\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"productInventory\"] });\r\n      toast.success(\"Adjustment status updated\", {\r\n        description:\r\n          \"The inventory adjustment status has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating adjustment status\", {\r\n        description:\r\n          error.message ||\r\n          \"An error occurred while updating the adjustment status.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useInventoryReportSummary(filters?: InventoryReportFilters) {\r\n  return useQuery<InventoryReportSummary>({\r\n    queryKey: [\"inventoryReportSummary\", filters],\r\n    queryFn: async () => {\r\n      try {\r\n        console.log(\"Fetching inventory report summary with filters:\", filters);\r\n        const result = await inventoryService.getInventoryReportSummary(\r\n          filters\r\n        );\r\n        console.log(\r\n          \"Inventory report summary API response:\",\r\n          JSON.stringify(result, null, 2)\r\n        );\r\n        return result as InventoryReportSummary;\r\n      } catch (error) {\r\n        console.error(\"Error fetching inventory report summary:\", error);\r\n        // Return default data structure on error\r\n        return {\r\n          total_products: 0,\r\n          total_value: 0,\r\n          low_stock_count: 0,\r\n          out_of_stock_count: 0,\r\n          top_products: [],\r\n          by_category: [],\r\n          by_branch: [],\r\n        };\r\n      }\r\n    },\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n  });\r\n}\r\n\r\nexport function useStockValuationReport(filters?: StockValuationReportFilters) {\r\n  return useQuery<StockValuationReport>({\r\n    queryKey: [\"stockValuationReport\", filters],\r\n    queryFn: async () => {\r\n      try {\r\n        console.log(\"Fetching stock valuation report with filters:\", filters);\r\n        const result = await inventoryService.getStockValuationReport(\r\n          filters\r\n        );\r\n        console.log(\r\n          \"Stock valuation report API response:\",\r\n          JSON.stringify(result, null, 2)\r\n        );\r\n        return result as StockValuationReport;\r\n      } catch (error) {\r\n        console.error(\"Error fetching stock valuation report:\", error);\r\n        // Return default data structure on error\r\n        return {\r\n          summary: {\r\n            total_items: 0,\r\n            total_quantity: 0,\r\n            total_value: 0,\r\n            valuation_methods: {\r\n              FIFO: { count: 0, value: 0 },\r\n              LIFO: { count: 0, value: 0 },\r\n              WEIGHTED_AVERAGE: { count: 0, value: 0 }\r\n            }\r\n          },\r\n          items: [],\r\n          by_category: [],\r\n          by_branch: [],\r\n          by_valuation_method: []\r\n        };\r\n      }\r\n    },\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n  });\r\n}\r\n\r\nexport function useStockAdjustmentTypes() {\r\n  return useQuery<StockAdjustmentType[]>({\r\n    queryKey: [\"stockAdjustmentTypes\"],\r\n    queryFn: async () => {\r\n      try {\r\n        const result = await inventoryService.getStockAdjustmentTypes();\r\n        return result;\r\n      } catch (error) {\r\n        console.error(\"Error fetching stock adjustment types:\", error);\r\n        throw error;\r\n      }\r\n    },\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n  });\r\n}\r\n\r\nexport function useStockItemsByBranch(branch_id: number | null) {\r\n  return useQuery<StockItem[]>({\r\n    queryKey: [\"stockItems\", branch_id],\r\n    queryFn: async () => {\r\n      if (!branch_id) return [];\r\n      try {\r\n        const result = await inventoryService.getStockItemsByBranch(branch_id);\r\n        return Array.isArray(result) ? result : result.data || [];\r\n      } catch (error) {\r\n        console.error(\"Error fetching stock items:\", error);\r\n        return [];\r\n      }\r\n    },\r\n    enabled: !!branch_id,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAEA;AAAA;AAAA;AACA;AAcA;;AAjBA;;;;AAoBO,SAAS,eACd,MAA4B,EAC5B,SAAmB;;IAEnB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAe;SAAO;QACjC,OAAO;uCAAE;gBACP,IAAI;oBACF,gCAAgC;oBAChC,MAAM,OAAO,QAAQ,QAAQ;oBAC7B,MAAM,QAAQ,QAAQ,SAAS;oBAE/B,MAAM,SAAS,MAAM,8JAAA,CAAA,mBAAgB,CAAC,cAAc,CAAC;oBAErD,gDAAgD;oBAChD,IAAI,CAAC,OAAO,UAAU,EAAE;wBACtB,OAAO,UAAU,GAAG;4BAClB,OAAO,OAAO,IAAI,CAAC,MAAM;4BACzB,MAAM,SAAS,MAAgB;4BAC/B,OAAO,SAAS,OAAiB;4BACjC,OAAO,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,GAAG,SAAS,OAAiB;wBAClE;oBACF,OAAO;wBACL,kDAAkD;wBAClD,MAAM,kBAAkB,KAAK,IAAI,CAAC,OAAO,UAAU,CAAC,KAAK,GAAG,SAAS,OAAiB,QAAQ;wBAE9F,6DAA6D;wBAC7D,MAAM,cAAc,OAAO,UAAU,CAAC,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,GAAG,mBAAmB;wBAElF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,CAAC,EAAE;4BACpC,UAAU,OAAO,UAAU,CAAC,KAAK;4BACjC;4BACA;4BACA,OAAO,OAAO,UAAU,CAAC,KAAK;4BAC9B;wBACF;wBAEA,wCAAwC;wBACxC,OAAO,UAAU,CAAC,KAAK,GAAG;wBAE1B,mDAAmD;wBACnD,IAAI,OAAO,UAAU,CAAC,IAAI,KAAK,SAAS,MAAgB,KAAK;4BAC3D,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM;4BAC/D,OAAO,UAAU,CAAC,IAAI,GAAG,SAAS,MAAgB;wBACpD;oBACF;oBAEA,QAAQ,GAAG,CAAC,8BAA8B;oBAC1C,QAAQ,GAAG,CAAC,oBAAoB,OAAO,UAAU;oBACjD,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAC9C,MAAM;gBACR;YACF;;QACA,SAAS,cAAc,YAAY,YAAY;QAC/C,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,MAAM;uCAAE,CAAC;gBACP,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,OAAO;wBACT;oBACF;gBACF;gBACA,OAAO;YACT;;IACF;AACF;GA1EgB;;QAIP,8KAAA,CAAA,WAAQ;;;AAyEV,SAAS,mBACd,QAAgB,EAChB,MAA4B,EAC5B,SAAmB;;IAEnB,QAAQ,GAAG,CAAC,mCAAmC;QAC7C;QACA;QACA;IACF;IAEA,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAmB;YAAU;SAAO;QAC/C,kCAAkC;QAClC,OAAO;2CAAE;gBACP,QAAQ,GAAG,CAAC,qCAAqC;oBAC/C;oBACA;gBACF;YACF;;QACA,OAAO;2CAAE;gBACP,IAAI;oBACF,QAAQ,GAAG,CAAC,kCAAkC;oBAC9C,QAAQ,GAAG,CAAC,gBAAgB;oBAE5B,sDAAsD;oBACtD,IAAI,aAAa,CAAC,GAAG;wBACnB,QAAQ,GAAG,CAAC;oBACd,OAAO;wBACL,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,SAAS,UAAU,CAAC;oBAClE;oBAEA,gCAAgC;oBAChC,MAAM,OAAO,QAAQ,QAAQ;oBAC7B,MAAM,QAAQ,QAAQ,SAAS;oBAC/B,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,KAAK,QAAQ,EAAE,OAAO;oBAEtD,MAAM,SAAS,MAAM,8JAAA,CAAA,mBAAgB,CAAC,kBAAkB,CACtD,UACA;oBAGF,gDAAgD;oBAChD,IAAI,CAAC,OAAO,UAAU,EAAE;wBACtB,QAAQ,GAAG,CAAC;wBACZ,OAAO,UAAU,GAAG;4BAClB,OAAO,OAAO,IAAI,CAAC,MAAM;4BACzB,MAAM,SAAS,MAAgB;4BAC/B,OAAO,SAAS,OAAiB;4BACjC,OAAO,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,GAAG,SAAS,OAAiB;wBAClE;oBACF,OAAO;wBACL,+DAA+D;wBAC/D,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM;wBAEnF,kDAAkD;wBAClD,MAAM,kBAAkB,KAAK,IAAI,CAAC,OAAO,UAAU,CAAC,KAAK,GAAG,SAAS,OAAiB,QAAQ;wBAE9F,6DAA6D;wBAC7D,MAAM,cAAc,OAAO,UAAU,CAAC,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,GAAG,mBAAmB;wBAElF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,CAAC,EAAE;4BACpC,UAAU,OAAO,UAAU,CAAC,KAAK;4BACjC;4BACA;4BACA,OAAO,OAAO,UAAU,CAAC,KAAK;4BAC9B;wBACF;wBAEA,wCAAwC;wBACxC,OAAO,UAAU,CAAC,KAAK,GAAG;wBAE1B,mDAAmD;wBACnD,IAAI,OAAO,UAAU,CAAC,IAAI,KAAK,SAAS,MAAgB,KAAK;4BAC3D,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM;4BAC/D,OAAO,UAAU,CAAC,IAAI,GAAG,SAAS,MAAgB;wBACpD;wBAEA,sDAAsD;wBACtD,OAAO,UAAU,CAAC,UAAU,GAAG,OAAO,UAAU,CAAC,KAAK;oBACxD;oBAEA,QAAQ,GAAG,CAAC,kCAAkC;oBAC9C,QAAQ,GAAG,CAAC,oBAAoB,OAAO,UAAU;oBACjD,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;oBAClD,MAAM;gBACR;YACF;;QACA,SAAS;2CAAC;gBACR,yDAAyD;gBACzD,IAAI,cAAc,WAAW,OAAO;gBAEpC,2EAA2E;gBAC3E,OAAO,aAAa;YACtB;SAAC;QACD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,MAAM;2CAAE,CAAC;gBACP,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,OAAO;wBACT;oBACF;gBACF;gBACA,OAAO;YACT;;IACF;AACF;IAnHgB;;QAWP,8KAAA,CAAA,WAAQ;;;AA0GV,SAAS,oBACd,SAAiB,EACjB,MAA4B;;IAE5B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAoB;YAAW;SAAO;QACjD,OAAO;4CAAE;gBACP,IAAI,CAAC,WAAW;oBACd,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,OAAO;wBACT;oBACF;gBACF;gBAEA,IAAI;oBACF,MAAM,SAAS,MAAM,8JAAA,CAAA,mBAAgB,CAAC,mBAAmB,CACvD,WACA;oBAEF,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,MAAM;gBACR;YACF;;QACA,SAAS,CAAC,CAAC;QACX,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,MAAM;4CAAE,CAAC;gBACP,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,OAAO;wBACT;oBACF;gBACF;gBACA,OAAO;YACT;;IACF;AACF;IAnDgB;;QAIP,8KAAA,CAAA,WAAQ;;;AAiDV,SAAS,yBAAyB,MAA4B;;IACnE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAyB;SAAO;QAC3C,OAAO;iDAAE;gBACP,IAAI;oBACF,MAAM,SAAS,MAAM,8JAAA,CAAA,mBAAgB,CAAC,wBAAwB,CAAC;oBAC/D,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0CAA0C;oBACxD,MAAM;gBACR;YACF;;QACA,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,MAAM;iDAAE,CAAC;gBACP,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,OAAO;wBACT;oBACF;gBACF;gBACA,OAAO;YACT;;IACF;AACF;IAhCgB;;QACP,8KAAA,CAAA,WAAQ;;;AAiCV,SAAS,wBAAwB,EAAU;;IAChD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAwB;SAAG;QACtC,OAAO;gDAAE,IAAM,8JAAA,CAAA,mBAAgB,CAAC,2BAA2B,CAAC;;QAC5D,SAAS,CAAC,CAAC;IACb;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAOV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yDAAE,CAAC,cACX,8JAAA,CAAA,mBAAgB,CAAC,0BAA0B,CAAC;;QAC9C,SAAS;yDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAwB;gBAAC;gBACpE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAkB;gBAAC;gBAC9D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAmB;gBAAC;gBAC/D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;oBACnC,aAAa;gBACf;YACF;;QACA,OAAO;yDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,8BAA8B;oBACxC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IApBgB;;QACM,yLAAA,CAAA,iBAAc;QAC3B,iLAAA,CAAA,cAAW;;;AAoBb,SAAS,wBAAwB,MAA4B;;IAClE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAwB;SAAO;QAC1C,OAAO;gDAAE,IAAM,8JAAA,CAAA,mBAAgB,CAAC,uBAAuB,CAAC;;QACxD,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;IACxB;AACF;IATgB;;QACP,8KAAA,CAAA,WAAQ;;;AAUV,SAAS,uBAAuB,EAAU;;IAC/C,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAuB;SAAG;QACrC,OAAO;+CAAE,IAAM,8JAAA,CAAA,mBAAgB,CAAC,0BAA0B,CAAC;;QAC3D,SAAS,CAAC,CAAC;IACb;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAOV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;wDAAE,CAAC,aACX,8JAAA,CAAA,mBAAgB,CAAC,yBAAyB,CAAC;;QAC7C,SAAS;wDAAE,CAAC,MAAM;gBAChB,2DAA2D;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAuB;gBAAC;gBACnE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAwB;gBAAC;gBACpE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAkB;gBAAC;gBAC9D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAc;gBAAC;gBAC1D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAmB;gBAAC;gBAE/D,uEAAuE;gBACvE,IAAI,UAAU,SAAS,EAAE;oBACvB,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAc,UAAU,SAAS;yBAAC;oBAAC;gBAChF;gBAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,sBAAsB;oBAClC,aAAa;gBACf;YACF;;QACA,OAAO;wDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,6BAA6B;oBACvC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IA9BgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA6Bb,SAAS,mCAAmC,EAAU;;IAC3D,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;8DAAE,CAAC,SACX,8JAAA,CAAA,mBAAgB,CAAC,+BAA+B,CAAC,IAAI;;QACvD,SAAS;8DAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAuB;gBAAC;gBACnE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAuB;qBAAG;gBAAC;gBACtE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAkB;gBAAC;gBAC9D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAmB;gBAAC;gBAC/D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,6BAA6B;oBACzC,aACE;gBACJ;YACF;;QACA,OAAO;8DAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,oCAAoC;oBAC9C,aACE,MAAM,OAAO,IACb;gBACJ;YACF;;IACF;AACF;IAxBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAuBb,SAAS,0BAA0B,OAAgC;;IACxE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAA0B;QACtC,UAAU;YAAC;YAA0B;SAAQ;QAC7C,OAAO;kDAAE;gBACP,IAAI;oBACF,QAAQ,GAAG,CAAC,mDAAmD;oBAC/D,MAAM,SAAS,MAAM,8JAAA,CAAA,mBAAgB,CAAC,yBAAyB,CAC7D;oBAEF,QAAQ,GAAG,CACT,0CACA,KAAK,SAAS,CAAC,QAAQ,MAAM;oBAE/B,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,4CAA4C;oBAC1D,yCAAyC;oBACzC,OAAO;wBACL,gBAAgB;wBAChB,aAAa;wBACb,iBAAiB;wBACjB,oBAAoB;wBACpB,cAAc,EAAE;wBAChB,aAAa,EAAE;wBACf,WAAW,EAAE;oBACf;gBACF;YACF;;QACA,OAAO;QACP,sBAAsB;IACxB;AACF;KA/BgB;;QACP,8KAAA,CAAA,WAAQ;;;AAgCV,SAAS,wBAAwB,OAAqC;;IAC3E,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAwB;QACpC,UAAU;YAAC;YAAwB;SAAQ;QAC3C,OAAO;gDAAE;gBACP,IAAI;oBACF,QAAQ,GAAG,CAAC,iDAAiD;oBAC7D,MAAM,SAAS,MAAM,8JAAA,CAAA,mBAAgB,CAAC,uBAAuB,CAC3D;oBAEF,QAAQ,GAAG,CACT,wCACA,KAAK,SAAS,CAAC,QAAQ,MAAM;oBAE/B,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0CAA0C;oBACxD,yCAAyC;oBACzC,OAAO;wBACL,SAAS;4BACP,aAAa;4BACb,gBAAgB;4BAChB,aAAa;4BACb,mBAAmB;gCACjB,MAAM;oCAAE,OAAO;oCAAG,OAAO;gCAAE;gCAC3B,MAAM;oCAAE,OAAO;oCAAG,OAAO;gCAAE;gCAC3B,kBAAkB;oCAAE,OAAO;oCAAG,OAAO;gCAAE;4BACzC;wBACF;wBACA,OAAO,EAAE;wBACT,aAAa,EAAE;wBACf,WAAW,EAAE;wBACb,qBAAqB,EAAE;oBACzB;gBACF;YACF;;QACA,OAAO;QACP,sBAAsB;IACxB;AACF;KAtCgB;;QACP,8KAAA,CAAA,WAAQ;;;AAuCV,SAAS;;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAyB;QACrC,UAAU;YAAC;SAAuB;QAClC,OAAO;gDAAE;gBACP,IAAI;oBACF,MAAM,SAAS,MAAM,8JAAA,CAAA,mBAAgB,CAAC,uBAAuB;oBAC7D,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0CAA0C;oBACxD,MAAM;gBACR;YACF;;QACA,OAAO;QACP,sBAAsB;IACxB;AACF;KAfgB;;QACP,8KAAA,CAAA,WAAQ;;;AAgBV,SAAS,sBAAsB,SAAwB;;IAC5D,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAe;QAC3B,UAAU;YAAC;YAAc;SAAU;QACnC,OAAO;8CAAE;gBACP,IAAI,CAAC,WAAW,OAAO,EAAE;gBACzB,IAAI;oBACF,MAAM,SAAS,MAAM,8JAAA,CAAA,mBAAgB,CAAC,qBAAqB,CAAC;oBAC5D,OAAO,MAAM,OAAO,CAAC,UAAU,SAAS,OAAO,IAAI,IAAI,EAAE;gBAC3D,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B;oBAC7C,OAAO,EAAE;gBACX;YACF;;QACA,SAAS,CAAC,CAAC;QACX,OAAO;QACP,sBAAsB;IACxB;AACF;KAjBgB;;QACP,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 10757, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/branches/api/branch-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { Branch, PaginatedResponse, UpdateBranchStatusRequest } from \"@/types\";\r\nimport { CreateBranchRequest, UpdateBranchRequest } from \"@/types/branch\";\r\n\r\nexport const branchService = {\r\n  getBranches: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Branch>> => {\r\n    try {\r\n      console.log(\"Calling API: /branches with params:\", params);\r\n      let response;\r\n\r\n      try {\r\n        // Try the Next.js API route first\r\n        response = await apiClient.get<any>(\"/branches\", { params });\r\n      } catch (error) {\r\n        console.warn(\r\n          \"Error fetching from Next.js API route, trying direct backend:\",\r\n          error\r\n        );\r\n        // If that fails, try the backend directly\r\n        const API_URL = process.env.NEXT_PUBLIC_API_URL;\r\n        const token =\r\n          localStorage.getItem(\"token\") || localStorage.getItem(\"accessToken\");\r\n\r\n        if (!token) {\r\n          throw new Error(\"No authentication token available\");\r\n        }\r\n\r\n        const backendResponse = await fetch(\r\n          `${API_URL}/branches${\r\n            params ? `?${new URLSearchParams(params)}` : \"\"\r\n          }`,\r\n          {\r\n            headers: {\r\n              Authorization: `Bearer ${token}`,\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n          }\r\n        );\r\n\r\n        if (!backendResponse.ok) {\r\n          throw new Error(`Backend API error: ${backendResponse.status}`);\r\n        }\r\n\r\n        response = await backendResponse.json();\r\n      }\r\n\r\n      console.log(\"Raw branches API response:\", response);\r\n\r\n      // Map API response to our Branch type\r\n      const mapApiBranchToBranch = (apiBranch: any): Branch => ({\r\n        ...apiBranch,\r\n      });\r\n\r\n      // If response is an array, convert to paginated format with mapped branches\r\n      if (Array.isArray(response)) {\r\n        console.log(\r\n          \"Branches response is an array, converting to paginated format\"\r\n        );\r\n        const mappedBranches = response.map(mapApiBranchToBranch);\r\n        return {\r\n          data: mappedBranches,\r\n          pagination: {\r\n            total: mappedBranches.length,\r\n            page: 1,\r\n            limit: mappedBranches.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        console.log(\"Branches response is already in paginated format\");\r\n        const mappedBranches = response.data.map(mapApiBranchToBranch);\r\n        return {\r\n          data: mappedBranches,\r\n          pagination: response.pagination || {\r\n            total: mappedBranches.length,\r\n            page: 1,\r\n            limit: mappedBranches.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If response has data property but it's not an array, wrap it\r\n      if (response && response.data && !Array.isArray(response.data)) {\r\n        console.log(\r\n          \"Branches response has data property but it's not an array, wrapping it\"\r\n        );\r\n        return {\r\n          data: [mapApiBranchToBranch(response.data)],\r\n          pagination: response.pagination || {\r\n            total: 1,\r\n            page: 1,\r\n            limit: 1,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If response itself is not an array but contains branches directly\r\n      if (response && !Array.isArray(response) && !response.data) {\r\n        console.log(\"Response contains branches directly, converting to array\");\r\n        // Try to extract branches from the response\r\n        const branches = Object.values(response).filter(\r\n          (item) =>\r\n            typeof item === \"object\" &&\r\n            item !== null &&\r\n            \"id\" in item &&\r\n            \"name\" in item\r\n        );\r\n\r\n        if (branches.length > 0) {\r\n          const mappedBranches = branches.map(mapApiBranchToBranch);\r\n          return {\r\n            data: mappedBranches,\r\n            pagination: {\r\n              total: mappedBranches.length,\r\n              page: 1,\r\n              limit: mappedBranches.length,\r\n              totalPages: 1,\r\n            },\r\n          };\r\n        }\r\n      }\r\n\r\n      // Default fallback\r\n      console.log(\"Using default fallback for branches response\");\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error in getBranches:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getBranchById: async (id: number): Promise<Branch> => {\r\n    return apiClient.get(`/branches/${id}`);\r\n  },\r\n\r\n  createBranch: async (branch: CreateBranchRequest): Promise<Branch> => {\r\n    // Send exactly what the API expects according to the guide\r\n    const apiRequest = {\r\n      tenant_id: branch.tenant_id,\r\n      name: branch.name,\r\n      location: branch.location,\r\n      region_id: branch.region_id,\r\n      is_hq: false, // Always set to false as requested\r\n      status: \"active\", // Default status\r\n    };\r\n\r\n    // Add optional fields if they exist\r\n    if (branch.phone) {\r\n      apiRequest.phone_number = branch.phone;\r\n    }\r\n    if (branch.email) {\r\n      apiRequest.email = branch.email;\r\n    }\r\n\r\n    console.log(\"Creating branch with data:\", apiRequest);\r\n    return apiClient.post(\"/branches\", apiRequest);\r\n  },\r\n\r\n  updateBranch: async (\r\n    id: number,\r\n    branch: UpdateBranchRequest\r\n  ): Promise<Branch> => {\r\n    // Ensure we're sending the correct fields to the API\r\n    const apiRequest = {\r\n      name: branch.name,\r\n      location: branch.location,\r\n      region_id: branch.region_id,\r\n      level: branch.level,\r\n    };\r\n\r\n    console.log(\"Updating branch with data:\", apiRequest);\r\n    return apiClient.put(`/branches/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteBranch: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/branches/${id}`);\r\n  },\r\n\r\n  updateBranchStatus: async (\r\n    id: number,\r\n    status: UpdateBranchStatusRequest\r\n  ): Promise<Branch> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      status: status.status, // 'active' or 'inactive'\r\n    };\r\n\r\n    return apiClient.put(`/branches/${id}/status`, apiRequest);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAqBwB;AArBxB;;AAIO,MAAM,gBAAgB;IAC3B,aAAa,OACX;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,uCAAuC;YACnD,IAAI;YAEJ,IAAI;gBACF,kCAAkC;gBAClC,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,aAAa;oBAAE;gBAAO;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CACV,iEACA;gBAEF,0CAA0C;gBAC1C,MAAM;gBACN,MAAM,QACJ,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;gBAExD,IAAI,CAAC,OAAO;oBACV,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAkB,MAAM,MAC5B,GAAG,QAAQ,SAAS,EAClB,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,SAAS,GAAG,IAC7C,EACF;oBACE,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,OAAO;wBAChC,gBAAgB;oBAClB;gBACF;gBAGF,IAAI,CAAC,gBAAgB,EAAE,EAAE;oBACvB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,gBAAgB,MAAM,EAAE;gBAChE;gBAEA,WAAW,MAAM,gBAAgB,IAAI;YACvC;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,sCAAsC;YACtC,MAAM,uBAAuB,CAAC,YAA2B,CAAC;oBACxD,GAAG,SAAS;gBACd,CAAC;YAED,4EAA4E;YAC5E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,QAAQ,GAAG,CACT;gBAEF,MAAM,iBAAiB,SAAS,GAAG,CAAC;gBACpC,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,eAAe,MAAM;wBAC5B,MAAM;wBACN,OAAO,eAAe,MAAM;wBAC5B,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,QAAQ,GAAG,CAAC;gBACZ,MAAM,iBAAiB,SAAS,IAAI,CAAC,GAAG,CAAC;gBACzC,OAAO;oBACL,MAAM;oBACN,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,eAAe,MAAM;wBAC5B,MAAM;wBACN,OAAO,eAAe,MAAM;wBAC5B,YAAY;oBACd;gBACF;YACF;YAEA,+DAA+D;YAC/D,IAAI,YAAY,SAAS,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC9D,QAAQ,GAAG,CACT;gBAEF,OAAO;oBACL,MAAM;wBAAC,qBAAqB,SAAS,IAAI;qBAAE;oBAC3C,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YAEA,oEAAoE;YACpE,IAAI,YAAY,CAAC,MAAM,OAAO,CAAC,aAAa,CAAC,SAAS,IAAI,EAAE;gBAC1D,QAAQ,GAAG,CAAC;gBACZ,4CAA4C;gBAC5C,MAAM,WAAW,OAAO,MAAM,CAAC,UAAU,MAAM,CAC7C,CAAC,OACC,OAAO,SAAS,YAChB,SAAS,QACT,QAAQ,QACR,UAAU;gBAGd,IAAI,SAAS,MAAM,GAAG,GAAG;oBACvB,MAAM,iBAAiB,SAAS,GAAG,CAAC;oBACpC,OAAO;wBACL,MAAM;wBACN,YAAY;4BACV,OAAO,eAAe,MAAM;4BAC5B,MAAM;4BACN,OAAO,eAAe,MAAM;4BAC5B,YAAY;wBACd;oBACF;gBACF;YACF;YAEA,mBAAmB;YACnB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,eAAe,OAAO;QACpB,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;IACxC;IAEA,cAAc,OAAO;QACnB,2DAA2D;QAC3D,MAAM,aAAa;YACjB,WAAW,OAAO,SAAS;YAC3B,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,OAAO;YACP,QAAQ;QACV;QAEA,oCAAoC;QACpC,IAAI,OAAO,KAAK,EAAE;YAChB,WAAW,YAAY,GAAG,OAAO,KAAK;QACxC;QACA,IAAI,OAAO,KAAK,EAAE;YAChB,WAAW,KAAK,GAAG,OAAO,KAAK;QACjC;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,aAAa;IACrC;IAEA,cAAc,OACZ,IACA;QAEA,qDAAqD;QACrD,MAAM,aAAa;YACjB,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,OAAO,OAAO,KAAK;QACrB;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;IAC1C;IAEA,cAAc,OAAO;QACnB,OAAO,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IAC3C;IAEA,oBAAoB,OAClB,IACA;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,QAAQ,OAAO,MAAM;QACvB;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE;IACjD;AACF", "debugId": null}}, {"offset": {"line": 10937, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/branches/hooks/use-branches.ts"], "sourcesContent": ["import { UpdateBranchRequest, UpdateBranchStatusRequest } from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport { branchService } from \"../api/branch-service\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { CreateBranchRequest } from \"@/types/branch\";\r\n\r\nexport function useBranches(\r\n  params?: Record<string, any>,\r\n  options?: { enabled?: boolean }\r\n) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n\r\n  return useQuery({\r\n    queryKey: [\"branches\", params],\r\n    queryFn: () => branchService.getBranches(params),\r\n    // Only run query when authentication is ready and enabled option is true (if provided)\r\n    enabled: isInitialized && !!accessToken && (options?.enabled !== false),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useBranch(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"branches\", id],\r\n    queryFn: () => branchService.getBranchById(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateBranch() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (branch: CreateBranchRequest) =>\r\n      branchService.createBranch(branch),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch created\", {\r\n        description: \"The branch has been created successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error creating branch\", {\r\n        description:\r\n          error.message || \"An error occurred while creating the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateBranch(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (branch: UpdateBranchRequest) =>\r\n      branchService.updateBranch(id, branch),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch updated\", {\r\n        description: \"The branch has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating branch\", {\r\n        description:\r\n          error.message || \"An error occurred while updating the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteBranch() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => branchService.deleteBranch(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch deleted\", {\r\n        description: \"The branch has been deleted successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error deleting branch\", {\r\n        description:\r\n          error.message || \"An error occurred while deleting the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateBranchStatus(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (status: UpdateBranchStatusRequest) =>\r\n      branchService.updateBranchStatus(id, status),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch status updated\", {\r\n        description: \"The branch status has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating branch status\", {\r\n        description:\r\n          error.message ||\r\n          \"An error occurred while updating the branch status.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAGO,SAAS,YACd,MAA4B,EAC5B,OAA+B;;IAE/B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAEnD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAO;QAC9B,OAAO;oCAAE,IAAM,0JAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;;QACzC,uFAAuF;QACvF,SAAS,iBAAiB,CAAC,CAAC,eAAgB,SAAS,YAAY;QACjE,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,MAAM;oCAAE,CAAC;gBACP,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;oBACF;gBACF;gBACA,OAAO;YACT;;IACF;AACF;GA/BgB;;QAIyB,wIAAA,CAAA,gBAAa;QAE7C,8KAAA,CAAA,WAAQ;;;AA2BV,SAAS,UAAU,EAAU;;IAClC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAG;QAC1B,OAAO;kCAAE,IAAM,0JAAA,CAAA,gBAAa,CAAC,aAAa,CAAC;;QAC3C,SAAS,CAAC,CAAC;IACb;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAOV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;;QAC7B,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IAnBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS,gBAAgB,EAAU;;IACxC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,IAAI;;QACjC,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IApBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,KAAe,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;;QACvD,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IAlBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,SAAS,sBAAsB,EAAU;;IAC9C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,kBAAkB,CAAC,IAAI;;QACvC,SAAS;iDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,yBAAyB;oBACrC,aAAa;gBACf;YACF;;QACA,OAAO;iDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;oBAC1C,aACE,MAAM,OAAO,IACb;gBACJ;YACF;;IACF;AACF;IArBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 11173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 11249, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/inventory/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useMemo } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { MainLayout } from \"@/components/layouts/main-layout\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { InventoryTable } from \"@/features/inventory/components/inventory-table\";\r\nimport { DsaStockTable } from \"@/features/inventory/components/dsa-stock-table\";\r\nimport {\r\n  useBranchInventory,\r\n  useHQInventory,\r\n} from \"@/features/inventory/hooks/use-inventory\";\r\nimport { useBranches } from \"@/features/branches/hooks/use-branches\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Package, AlertTriangle, DollarSign } from \"lucide-react\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\";\r\n\r\nexport default function InventoryPage() {\r\n  // Get current user and ensure authentication is set up\r\n  const { data: userData, isLoading: isLoadingUser } = useCurrentUser();\r\n\r\n  // Log authentication status for debugging\r\n  console.log(\"Auth status:\", { userData, isLoadingUser });\r\n  const { data: branchesData, isLoading: isLoadingBranches } = useBranches();\r\n  const [selectedBranchId, setSelectedBranchId] = useState<\r\n    number | undefined\r\n  >();\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage, setItemsPerPage] = useState(10);\r\n\r\n  // Debug logging\r\n  console.log(\"branchesData:\", branchesData);\r\n  console.log(\"branchesData?.data:\", branchesData?.data);\r\n\r\n  // Use the first branch as default if no branch is selected\r\n  // Ensure we have a valid branch ID for the API call\r\n  const branches = useMemo(() => {\r\n    const result = Array.isArray(branchesData?.data) ? branchesData.data : [];\r\n    console.log(\"Available branches:\", result);\r\n    return result;\r\n  }, [branchesData?.data]);\r\n\r\n  // Check if user is super_admin or company_admin\r\n  const isAdminUser = useMemo(() => {\r\n    const result =\r\n      userData?.role_name &&\r\n      [\r\n        \"super_admin\",\r\n        \"company_admin\",\r\n        \"tenant_admin\", // Added tenant_admin\r\n      ].includes(userData.role_name.toLowerCase());\r\n    console.log(\"User role:\", userData?.role_name, \"isAdminUser:\", result);\r\n    return result;\r\n  }, [userData?.role_name]);\r\n\r\n  // Use useEffect to set the selectedBranchId when user data or branches change\r\n  // This prevents infinite re-renders\r\n  useEffect(() => {\r\n    // Only set the selectedBranchId if it's not already set and we have the necessary data\r\n    if (\r\n      selectedBranchId === undefined &&\r\n      !isLoadingUser &&\r\n      !isLoadingBranches\r\n    ) {\r\n      // Check if user is company_admin or super_admin\r\n      const isCompanyAdmin =\r\n        userData?.role_name?.toLowerCase() === \"company_admin\";\r\n      const isSuperAdmin = userData?.role_name?.toLowerCase() === \"super_admin\";\r\n      const isTenantAdmin =\r\n        userData?.role_name?.toLowerCase() === \"tenant_admin\";\r\n\r\n      if (isCompanyAdmin || isSuperAdmin || isTenantAdmin) {\r\n        console.log(\r\n          \"Setting selectedBranchId to All Branches (-1) for admin user\"\r\n        );\r\n        setSelectedBranchId(-1); // Use -1 to indicate All Branches for admin users\r\n      } else if (branches.length > 0 && branches[0]?.id) {\r\n        console.log(\r\n          \"Setting selectedBranchId to first branch:\",\r\n          branches[0].id\r\n        );\r\n        setSelectedBranchId(branches[0].id);\r\n      }\r\n    }\r\n\r\n    // Force a log of the current state for debugging\r\n    console.log(\"Current state:\", {\r\n      selectedBranchId,\r\n      isAdminUser,\r\n      userRole: userData?.role_name,\r\n      branchesCount: branches.length,\r\n    });\r\n  }, [\r\n    isLoadingUser,\r\n    isLoadingBranches,\r\n    isAdminUser,\r\n    branches,\r\n    selectedBranchId,\r\n    userData?.role_name,\r\n  ]);\r\n\r\n  // Use selectedBranchId or first branch id if available\r\n  // If selectedBranchId is -1, it means \"All Branches\"\r\n  const effectiveBranchId = useMemo(() => {\r\n    const result =\r\n      selectedBranchId === -1\r\n        ? -1\r\n        : selectedBranchId ||\r\n          (branches.length > 0 ? branches[0]?.id : undefined);\r\n    console.log(\"effectiveBranchId:\", result);\r\n    return result;\r\n  }, [selectedBranchId, branches]);\r\n\r\n  // Check if we should enable the inventory query\r\n  const shouldEnableInventoryQuery = useMemo(() => {\r\n    const result =\r\n      !isLoadingUser &&\r\n      !!userData &&\r\n      (!!effectiveBranchId || effectiveBranchId === -1);\r\n    console.log(\"Should enable inventory query:\", result, {\r\n      isLoadingUser,\r\n      hasUserData: !!userData,\r\n      effectiveBranchId,\r\n    });\r\n    return result;\r\n  }, [isLoadingUser, userData, effectiveBranchId]);\r\n\r\n  // Determine which hook to use based on the user's role and selected branch\r\n  const useHQQuery = isAdminUser && selectedBranchId === 1; // Only use HQ query when explicitly selecting branch_id=1\r\n  console.log(\"Using HQ query:\", useHQQuery, { isAdminUser, selectedBranchId });\r\n\r\n  // Build query parameters for API calls\r\n  const queryParams = {\r\n    page: currentPage,\r\n    limit: itemsPerPage,\r\n    search: searchQuery || undefined,\r\n    product_name: searchQuery || undefined,\r\n  };\r\n\r\n  // For HQ inventory (admin users viewing HQ)\r\n  const { data: hqInventoryData, isLoading: isLoadingHQInventory } =\r\n    useHQInventory(\r\n      queryParams,\r\n      // Only enable when user data is loaded and we're using HQ query\r\n      Boolean(shouldEnableInventoryQuery && useHQQuery)\r\n    );\r\n\r\n  // For branch-specific inventory\r\n  const { data: branchInventoryData, isLoading: isLoadingBranchInventory } =\r\n    useBranchInventory(\r\n      typeof effectiveBranchId === \"number\" ? effectiveBranchId : 0,\r\n      queryParams,\r\n      // Only enable when user data is loaded, we have a branch ID, and we're not using HQ query\r\n      shouldEnableInventoryQuery && !useHQQuery\r\n    );\r\n\r\n  // Combine the data based on which query is active\r\n  const inventoryData = useHQQuery ? hqInventoryData : branchInventoryData;\r\n  const isLoadingInventory = useHQQuery\r\n    ? isLoadingHQInventory\r\n    : isLoadingBranchInventory;\r\n\r\n  console.log(\"inventoryData:\", inventoryData);\r\n\r\n  // We already have branches data defined above\r\n  console.log(\"Processed branches:\", branches);\r\n\r\n  // Safely handle inventory data\r\n  const inventory = useMemo(() => {\r\n    return Array.isArray(inventoryData?.data) ? inventoryData.data : [];\r\n  }, [inventoryData?.data]);\r\n  console.log(\"Processed inventory:\", inventory);\r\n\r\n  // Check pagination structure for compatibility\r\n\r\n  const handleBranchChange = (branchId: string) => {\r\n    const parsedBranchId = parseInt(branchId, 10);\r\n    setSelectedBranchId(parsedBranchId);\r\n    setCurrentPage(1);\r\n\r\n    // Log additional information for debugging\r\n    if (parsedBranchId === 1) {\r\n      console.log(\"Headquarters (HQ) branch selected\");\r\n    } else if (parsedBranchId === -1) {\r\n      console.log(\"All branches selected\");\r\n    } else {\r\n      console.log(`Branch with ID ${parsedBranchId} selected`);\r\n    }\r\n  };\r\n\r\n  const handleSearch = (query: string) => {\r\n    setSearchQuery(query);\r\n    setCurrentPage(1); // Reset to first page when searching\r\n    console.log(\"Searching inventory by product name:\", query);\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n    console.log(`Changing page to ${page}`);\r\n  };\r\n\r\n  const handleItemsPerPageChange = (value: string) => {\r\n    const numValue = parseInt(value, 10);\r\n    setItemsPerPage(numValue);\r\n    setCurrentPage(1); // Reset to first page when changing items per page\r\n    console.log(`Changing items per page to ${numValue}`);\r\n  };\r\n\r\n  // Ensure we have valid pagination values\r\n  const paginationTotal = inventoryData?.pagination?.total || 0;\r\n  // API returns 'pages' not 'totalPages'\r\n  const totalPages =\r\n    Math.max(\r\n      inventoryData?.pagination?.pages || 0,\r\n      inventoryData?.pagination?.totalPages || 0,\r\n      Math.ceil(paginationTotal / itemsPerPage)\r\n    ) || 1;\r\n\r\n  // Calculate low stock items\r\n  const lowStockItems =\r\n    inventory.length > 0\r\n      ? inventory.filter(\r\n          (item: any) =>\r\n            item.reorder_level && item.quantity <= item.reorder_level\r\n        ).length\r\n      : 0;\r\n\r\n  // Calculate out of stock items\r\n  const outOfStockItems =\r\n    inventory.length > 0\r\n      ? inventory.filter((item: any) => item.quantity === 0).length\r\n      : 0;\r\n\r\n  // Calculate total inventory value\r\n  const totalValue =\r\n    inventory.length > 0\r\n      ? inventory.reduce((total: number, item: any) => {\r\n          // Parse selling_price from string to number, preferring default_selling_price if available\r\n          const sellingPrice =\r\n            (item.default_selling_price\r\n              ? parseFloat(item.default_selling_price)\r\n              : 0) ||\r\n            (item.selling_price ? parseFloat(item.selling_price) : 0) ||\r\n            0;\r\n          return total + sellingPrice * item.quantity;\r\n        }, 0)\r\n      : 0;\r\n\r\n  // Show loading state if user data or branches are still loading\r\n  if (isLoadingUser || isLoadingBranches) {\r\n    return (\r\n      <MainLayout>\r\n        <div className=\"flex h-full items-center justify-center\">\r\n          <div className=\"text-center\">\r\n            <h2 className=\"text-xl font-semibold mb-2\">Loading...</h2>\r\n            <p className=\"text-muted-foreground\">\r\n              Please wait while we load your inventory data.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </MainLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"space-y-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold tracking-tight\">\r\n            Inventory Management\r\n          </h1>\r\n          <p className=\"text-muted-foreground\">\r\n            Manage your inventory across branches\r\n          </p>\r\n        </div>\r\n\r\n        <Tabs defaultValue=\"inventory\" className=\"space-y-4\">\r\n          <TabsList>\r\n            <TabsTrigger value=\"inventory\">Inventory</TabsTrigger>\r\n            <TabsTrigger value=\"dsa-stock\">DSA Stock</TabsTrigger>\r\n            {/* Removed Transfers tab as requested */}\r\n            {/* Removed Adjustments tab as requested */}\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"inventory\" className=\"space-y-4\">\r\n            <div className=\"grid gap-4 md:grid-cols-4\">\r\n              <Card>\r\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                  <CardTitle className=\"text-sm font-medium\">\r\n                    Total Items\r\n                  </CardTitle>\r\n                  <Package className=\"h-4 w-4 text-muted-foreground\" />\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"text-2xl font-bold\">\r\n                    {isLoadingInventory\r\n                      ? \"...\"\r\n                      : inventoryData?.pagination?.total || 0}\r\n                  </div>\r\n                  <p className=\"text-xs text-muted-foreground\">\r\n                    Products in inventory\r\n                  </p>\r\n                </CardContent>\r\n              </Card>\r\n              <Card>\r\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                  <CardTitle className=\"text-sm font-medium\">\r\n                    Low Stock Items\r\n                  </CardTitle>\r\n                  <AlertTriangle className=\"h-4 w-4 text-amber-500\" />\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"text-2xl font-bold\">\r\n                    {isLoadingInventory ? \"...\" : lowStockItems}\r\n                  </div>\r\n                  <p className=\"text-xs text-muted-foreground\">\r\n                    Items below reorder level\r\n                  </p>\r\n                </CardContent>\r\n              </Card>\r\n              <Card>\r\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                  <CardTitle className=\"text-sm font-medium\">\r\n                    Out of Stock\r\n                  </CardTitle>\r\n                  <AlertTriangle className=\"h-4 w-4 text-destructive\" />\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"text-2xl font-bold\">\r\n                    {isLoadingInventory ? \"...\" : outOfStockItems}\r\n                  </div>\r\n                  <p className=\"text-xs text-muted-foreground\">\r\n                    Items with zero stock\r\n                  </p>\r\n                </CardContent>\r\n              </Card>\r\n              <Card>\r\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                  <CardTitle className=\"text-sm font-medium\">\r\n                    Total Value\r\n                  </CardTitle>\r\n                  <DollarSign className=\"h-4 w-4 text-green-500\" />\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"text-2xl font-bold\">\r\n                    {isLoadingInventory ? \"...\" : formatCurrency(totalValue)}\r\n                  </div>\r\n                  <p className=\"text-xs text-muted-foreground\">\r\n                    Total inventory value\r\n                  </p>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"w-full max-w-xs\">\r\n                {/* Branch selection dropdown */}\r\n                {branches.length > 0 ? (\r\n                  <Select\r\n                    value={\r\n                      selectedBranchId !== undefined\r\n                        ? selectedBranchId.toString()\r\n                        : \"-1\" // Default to All Branches (-1) if no branch is selected\r\n                    }\r\n                    onValueChange={handleBranchChange}\r\n                    disabled={isLoadingBranches || branches.length === 0}\r\n                  >\r\n                    <SelectTrigger>\r\n                      <SelectValue placeholder=\"Select branch\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {/* Removed custom HQ option */}\r\n                      {/* Add \"All Branches\" option only for super_admin and company_admin */}\r\n                      {isAdminUser && (\r\n                        <SelectItem key=\"all-branches\" value=\"-1\">\r\n                          All Branches\r\n                        </SelectItem>\r\n                      )}\r\n                      {branches.map((branch) => (\r\n                        <SelectItem\r\n                          key={branch.id}\r\n                          value={branch.id?.toString() || \"\"}\r\n                        >\r\n                          {branch.name}\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                ) : (\r\n                  <div className=\"p-2 border rounded-md bg-muted\">\r\n                    <p className=\"text-sm text-muted-foreground\">\r\n                      No branches available\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              {/* Removed Manage HQ Stock button as requested */}\r\n            </div>\r\n\r\n            {branches.length > 0 ? (\r\n              effectiveBranchId ? (\r\n                <InventoryTable\r\n                  inventory={inventory}\r\n                  isLoading={isLoadingInventory || isLoadingBranches}\r\n                  onSearch={handleSearch}\r\n                  pagination={{\r\n                    page: currentPage,\r\n                    totalPages: totalPages,\r\n                    onPageChange: handlePageChange,\r\n                    limit: itemsPerPage,\r\n                    onItemsPerPageChange: handleItemsPerPageChange,\r\n                    total: paginationTotal,\r\n                  }}\r\n                />\r\n              ) : (\r\n                <div className=\"p-8 text-center\">\r\n                  <p>Error loading branch data. Please try again.</p>\r\n                </div>\r\n              )\r\n            ) : (\r\n              <div className=\"p-8 border rounded-md bg-muted text-center\">\r\n                <h3 className=\"text-lg font-medium mb-2\">\r\n                  No Branches Available\r\n                </h3>\r\n                <p className=\"text-muted-foreground mb-4\">\r\n                  You need to create at least one branch to manage inventory.\r\n                </p>\r\n                <Button asChild>\r\n                  <Link href=\"/branches/create\">Create Branch</Link>\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"dsa-stock\" className=\"space-y-4\">\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n              <div className=\"w-full max-w-xs\">\r\n                {/* Branch selection dropdown */}\r\n                {branches.length > 0 ? (\r\n                  <Select\r\n                    value={\r\n                      selectedBranchId !== undefined\r\n                        ? selectedBranchId.toString()\r\n                        : \"-1\" // Default to All Branches (-1) if no branch is selected\r\n                    }\r\n                    onValueChange={handleBranchChange}\r\n                    disabled={isLoadingBranches || branches.length === 0}\r\n                  >\r\n                    <SelectTrigger>\r\n                      <SelectValue placeholder=\"Select branch\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      {/* Removed custom HQ option */}\r\n                      {/* Add \"All Branches\" option only for super_admin and company_admin */}\r\n                      {isAdminUser && (\r\n                        <SelectItem key=\"all-branches\" value=\"-1\">\r\n                          All Branches\r\n                        </SelectItem>\r\n                      )}\r\n                      {branches.map((branch) => (\r\n                        <SelectItem\r\n                          key={branch.id}\r\n                          value={branch.id?.toString() || \"\"}\r\n                        >\r\n                          {branch.name}\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                ) : (\r\n                  <div className=\"p-2 border rounded-md bg-muted\">\r\n                    <p className=\"text-sm text-muted-foreground\">\r\n                      No branches available\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n            <DsaStockTable\r\n              branchId={\r\n                effectiveBranchId === -1 ? undefined : effectiveBranchId\r\n              }\r\n            />\r\n          </TabsContent>\r\n\r\n          {/* Removed Transfers tab content as requested */}\r\n\r\n          {/* Removed Adjustments tab content as requested */}\r\n        </Tabs>\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AACA;AAOA;AACA;AACA;;;AAxBA;;;;;;;;;;;;;;;AA0Be,SAAS;;IACtB,uDAAuD;IACvD,MAAM,EAAE,MAAM,QAAQ,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAElE,0CAA0C;IAC1C,QAAQ,GAAG,CAAC,gBAAgB;QAAE;QAAU;IAAc;IACtD,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,iBAAiB,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD;IACvE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAGvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gBAAgB;IAChB,QAAQ,GAAG,CAAC,iBAAiB;IAC7B,QAAQ,GAAG,CAAC,uBAAuB,cAAc;IAEjD,2DAA2D;IAC3D,oDAAoD;IACpD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2CAAE;YACvB,MAAM,SAAS,MAAM,OAAO,CAAC,cAAc,QAAQ,aAAa,IAAI,GAAG,EAAE;YACzE,QAAQ,GAAG,CAAC,uBAAuB;YACnC,OAAO;QACT;0CAAG;QAAC,cAAc;KAAK;IAEvB,gDAAgD;IAChD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YAC1B,MAAM,SACJ,UAAU,aACV;gBACE;gBACA;gBACA;aACD,CAAC,QAAQ,CAAC,SAAS,SAAS,CAAC,WAAW;YAC3C,QAAQ,GAAG,CAAC,cAAc,UAAU,WAAW,gBAAgB;YAC/D,OAAO;QACT;6CAAG;QAAC,UAAU;KAAU;IAExB,8EAA8E;IAC9E,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,uFAAuF;YACvF,IACE,qBAAqB,aACrB,CAAC,iBACD,CAAC,mBACD;gBACA,gDAAgD;gBAChD,MAAM,iBACJ,UAAU,WAAW,kBAAkB;gBACzC,MAAM,eAAe,UAAU,WAAW,kBAAkB;gBAC5D,MAAM,gBACJ,UAAU,WAAW,kBAAkB;gBAEzC,IAAI,kBAAkB,gBAAgB,eAAe;oBACnD,QAAQ,GAAG,CACT;oBAEF,oBAAoB,CAAC,IAAI,kDAAkD;gBAC7E,OAAO,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,IAAI;oBACjD,QAAQ,GAAG,CACT,6CACA,QAAQ,CAAC,EAAE,CAAC,EAAE;oBAEhB,oBAAoB,QAAQ,CAAC,EAAE,CAAC,EAAE;gBACpC;YACF;YAEA,iDAAiD;YACjD,QAAQ,GAAG,CAAC,kBAAkB;gBAC5B;gBACA;gBACA,UAAU,UAAU;gBACpB,eAAe,SAAS,MAAM;YAChC;QACF;kCAAG;QACD;QACA;QACA;QACA;QACA;QACA,UAAU;KACX;IAED,uDAAuD;IACvD,qDAAqD;IACrD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE;YAChC,MAAM,SACJ,qBAAqB,CAAC,IAClB,CAAC,IACD,oBACA,CAAC,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,EAAE,KAAK,SAAS;YACxD,QAAQ,GAAG,CAAC,sBAAsB;YAClC,OAAO;QACT;mDAAG;QAAC;QAAkB;KAAS;IAE/B,gDAAgD;IAChD,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6DAAE;YACzC,MAAM,SACJ,CAAC,iBACD,CAAC,CAAC,YACF,CAAC,CAAC,CAAC,qBAAqB,sBAAsB,CAAC,CAAC;YAClD,QAAQ,GAAG,CAAC,kCAAkC,QAAQ;gBACpD;gBACA,aAAa,CAAC,CAAC;gBACf;YACF;YACA,OAAO;QACT;4DAAG;QAAC;QAAe;QAAU;KAAkB;IAE/C,2EAA2E;IAC3E,MAAM,aAAa,eAAe,qBAAqB,GAAG,0DAA0D;IACpH,QAAQ,GAAG,CAAC,mBAAmB,YAAY;QAAE;QAAa;IAAiB;IAE3E,uCAAuC;IACvC,MAAM,cAAc;QAClB,MAAM;QACN,OAAO;QACP,QAAQ,eAAe;QACvB,cAAc,eAAe;IAC/B;IAEA,4CAA4C;IAC5C,MAAM,EAAE,MAAM,eAAe,EAAE,WAAW,oBAAoB,EAAE,GAC9D,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EACX,aACA,gEAAgE;IAChE,QAAQ,8BAA8B;IAG1C,gCAAgC;IAChC,MAAM,EAAE,MAAM,mBAAmB,EAAE,WAAW,wBAAwB,EAAE,GACtE,CAAA,GAAA,4JAAA,CAAA,qBAAkB,AAAD,EACf,OAAO,sBAAsB,WAAW,oBAAoB,GAC5D,aACA,0FAA0F;IAC1F,8BAA8B,CAAC;IAGnC,kDAAkD;IAClD,MAAM,gBAAgB,aAAa,kBAAkB;IACrD,MAAM,qBAAqB,aACvB,uBACA;IAEJ,QAAQ,GAAG,CAAC,kBAAkB;IAE9B,8CAA8C;IAC9C,QAAQ,GAAG,CAAC,uBAAuB;IAEnC,+BAA+B;IAC/B,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACxB,OAAO,MAAM,OAAO,CAAC,eAAe,QAAQ,cAAc,IAAI,GAAG,EAAE;QACrE;2CAAG;QAAC,eAAe;KAAK;IACxB,QAAQ,GAAG,CAAC,wBAAwB;IAEpC,+CAA+C;IAE/C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,iBAAiB,SAAS,UAAU;QAC1C,oBAAoB;QACpB,eAAe;QAEf,2CAA2C;QAC3C,IAAI,mBAAmB,GAAG;YACxB,QAAQ,GAAG,CAAC;QACd,OAAO,IAAI,mBAAmB,CAAC,GAAG;YAChC,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,eAAe,SAAS,CAAC;QACzD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,eAAe,IAAI,qCAAqC;QACxD,QAAQ,GAAG,CAAC,wCAAwC;IACtD;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;QACf,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,MAAM;IACxC;IAEA,MAAM,2BAA2B,CAAC;QAChC,MAAM,WAAW,SAAS,OAAO;QACjC,gBAAgB;QAChB,eAAe,IAAI,mDAAmD;QACtE,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,UAAU;IACtD;IAEA,yCAAyC;IACzC,MAAM,kBAAkB,eAAe,YAAY,SAAS;IAC5D,uCAAuC;IACvC,MAAM,aACJ,KAAK,GAAG,CACN,eAAe,YAAY,SAAS,GACpC,eAAe,YAAY,cAAc,GACzC,KAAK,IAAI,CAAC,kBAAkB,kBACzB;IAEP,4BAA4B;IAC5B,MAAM,gBACJ,UAAU,MAAM,GAAG,IACf,UAAU,MAAM,CACd,CAAC,OACC,KAAK,aAAa,IAAI,KAAK,QAAQ,IAAI,KAAK,aAAa,EAC3D,MAAM,GACR;IAEN,+BAA+B;IAC/B,MAAM,kBACJ,UAAU,MAAM,GAAG,IACf,UAAU,MAAM,CAAC,CAAC,OAAc,KAAK,QAAQ,KAAK,GAAG,MAAM,GAC3D;IAEN,kCAAkC;IAClC,MAAM,aACJ,UAAU,MAAM,GAAG,IACf,UAAU,MAAM,CAAC,CAAC,OAAe;QAC/B,2FAA2F;QAC3F,MAAM,eACJ,CAAC,KAAK,qBAAqB,GACvB,WAAW,KAAK,qBAAqB,IACrC,CAAC,KACL,CAAC,KAAK,aAAa,GAAG,WAAW,KAAK,aAAa,IAAI,CAAC,KACxD;QACF,OAAO,QAAQ,eAAe,KAAK,QAAQ;IAC7C,GAAG,KACH;IAEN,gEAAgE;IAChE,IAAI,iBAAiB,mBAAmB;QACtC,qBACE,6LAAC,kJAAA,CAAA,aAAU;sBACT,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAO/C;IAEA,qBACE,6LAAC,kJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAGlD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC,mIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAY,WAAU;;sCACvC,6LAAC,mIAAA,CAAA,WAAQ;;8CACP,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;8CAC/B,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;;;;;;;sCAKjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;;8CACvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;oDAAC,WAAU;;sEACpB,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAsB;;;;;;sEAG3C,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;;8DAErB,6LAAC,mIAAA,CAAA,cAAW;;sEACV,6LAAC;4DAAI,WAAU;sEACZ,qBACG,QACA,eAAe,YAAY,SAAS;;;;;;sEAE1C,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAKjD,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;oDAAC,WAAU;;sEACpB,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAsB;;;;;;sEAG3C,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;;8DAE3B,6LAAC,mIAAA,CAAA,cAAW;;sEACV,6LAAC;4DAAI,WAAU;sEACZ,qBAAqB,QAAQ;;;;;;sEAEhC,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAKjD,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;oDAAC,WAAU;;sEACpB,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAsB;;;;;;sEAG3C,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;;8DAE3B,6LAAC,mIAAA,CAAA,cAAW;;sEACV,6LAAC;4DAAI,WAAU;sEACZ,qBAAqB,QAAQ;;;;;;sEAEhC,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAKjD,6LAAC,mIAAA,CAAA,OAAI;;8DACH,6LAAC,mIAAA,CAAA,aAAU;oDAAC,WAAU;;sEACpB,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAsB;;;;;;sEAG3C,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;8DAExB,6LAAC,mIAAA,CAAA,cAAW;;sEACV,6LAAC;4DAAI,WAAU;sEACZ,qBAAqB,QAAQ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;sEAE/C,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;;8CAOnD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDAEZ,SAAS,MAAM,GAAG,kBACjB,6LAAC,qIAAA,CAAA,SAAM;4CACL,OACE,qBAAqB,YACjB,iBAAiB,QAAQ,KACzB,KAAK,wDAAwD;;4CAEnE,eAAe;4CACf,UAAU,qBAAqB,SAAS,MAAM,KAAK;;8DAEnD,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;wDAGX,6BACC,6LAAC,qIAAA,CAAA,aAAU;4DAAoB,OAAM;sEAAK;2DAA1B;;;;;wDAIjB,SAAS,GAAG,CAAC,CAAC,uBACb,6LAAC,qIAAA,CAAA,aAAU;gEAET,OAAO,OAAO,EAAE,EAAE,cAAc;0EAE/B,OAAO,IAAI;+DAHP,OAAO,EAAE;;;;;;;;;;;;;;;;iEAStB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;gCASpD,SAAS,MAAM,GAAG,IACjB,kCACE,6LAAC,oKAAA,CAAA,iBAAc;oCACb,WAAW;oCACX,WAAW,sBAAsB;oCACjC,UAAU;oCACV,YAAY;wCACV,MAAM;wCACN,YAAY;wCACZ,cAAc;wCACd,OAAO;wCACP,sBAAsB;wCACtB,OAAO;oCACT;;;;;yDAGF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;kDAAE;;;;;;;;;;yDAIP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2B;;;;;;sDAGzC,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;sDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAmB;;;;;;;;;;;;;;;;;;;;;;;sCAMtC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;;8CACvC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDAEZ,SAAS,MAAM,GAAG,kBACjB,6LAAC,qIAAA,CAAA,SAAM;4CACL,OACE,qBAAqB,YACjB,iBAAiB,QAAQ,KACzB,KAAK,wDAAwD;;4CAEnE,eAAe;4CACf,UAAU,qBAAqB,SAAS,MAAM,KAAK;;8DAEnD,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;wDAGX,6BACC,6LAAC,qIAAA,CAAA,aAAU;4DAAoB,OAAM;sEAAK;2DAA1B;;;;;wDAIjB,SAAS,GAAG,CAAC,CAAC,uBACb,6LAAC,qIAAA,CAAA,aAAU;gEAET,OAAO,OAAO,EAAE,EAAE,cAAc;0EAE/B,OAAO,IAAI;+DAHP,OAAO,EAAE;;;;;;;;;;;;;;;;iEAStB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;8CAOrD,6LAAC,uKAAA,CAAA,gBAAa;oCACZ,UACE,sBAAsB,CAAC,IAAI,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvD;GA5dwB;;QAE+B,kJAAA,CAAA,iBAAc;QAIN,0JAAA,CAAA,cAAW;QAuHtE,4JAAA,CAAA,iBAAc;QAQd,4JAAA,CAAA,qBAAkB;;;KArIE", "debugId": null}}]}