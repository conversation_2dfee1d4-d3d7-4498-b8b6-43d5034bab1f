{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/logo.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface LogoProps {\r\n  size?: \"sm\" | \"md\" | \"lg\";\r\n  className?: string;\r\n  variant?: \"default\" | \"white\";\r\n}\r\n\r\nexport function Logo({ size = \"md\", className, variant = \"default\" }: LogoProps) {\r\n  const sizeClasses = {\r\n    sm: \"text-xl\",\r\n    md: \"text-2xl\",\r\n    lg: \"text-3xl\",\r\n  };\r\n\r\n  const colorClasses = {\r\n    default: \"text-primary\",\r\n    white: \"text-white\",\r\n  };\r\n\r\n  return (\r\n    <div className={cn(\"font-bold tracking-tight\", sizeClasses[size], colorClasses[variant], className)}>\r\n      <span className=\"flex items-center gap-1\">\r\n        <span className=\"rounded-md bg-primary px-1.5 py-0.5 text-white\">Simba</span>\r\n        <span>POS</span>\r\n      </span>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUO,SAAS,KAAK,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,UAAU,SAAS,EAAa;IAC7E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,QAAQ,EAAE;kBACvF,cAAA,8OAAC;YAAK,WAAU;;8BACd,8OAAC;oBAAK,WAAU;8BAAiD;;;;;;8BACjE,8OAAC;8BAAK;;;;;;;;;;;;;;;;;AAId", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/auth-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { ReactNode, useEffect } from \"react\";\r\nimport { Logo } from \"@/components/ui/logo\";\r\n\r\ninterface AuthLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function AuthLayout({ children }: AuthLayoutProps) {\r\n  const { isInitialized, accessToken } = useAuthTokens();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Effect to handle auth initialization and loading state\r\n  useEffect(() => {\r\n    // Only show loading on the very first render if:\r\n    // 1. Auth is not initialized yet\r\n    // 2. We haven't shown the loading screen before in this session\r\n    if (!isInitialized && !hasShownLoading.auth) {\r\n      setLoading(\"auth\", true);\r\n    } else {\r\n      setLoading(\"auth\", false);\r\n    }\r\n\r\n    // If auth is initialized, we can hide the loading screen\r\n    if (isInitialized) {\r\n      setLoading(\"auth\", false);\r\n\r\n      // If we have an access token, mark that we've shown loading\r\n      // This prevents showing loading on subsequent navigations\r\n      if (accessToken) {\r\n        markLoadingShown(\"auth\");\r\n      }\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isInitialized, accessToken, hasShownLoading.auth]);\r\n\r\n  // Safety timeout to prevent getting stuck in loading state\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (isLoading.auth) {\r\n        setLoading(\"auth\", false);\r\n        markLoadingShown(\"auth\");\r\n      }\r\n    }, 1500); // 1.5 second timeout for better UX\r\n\r\n    return () => clearTimeout(timeoutId);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.auth]);\r\n\r\n  // Show loading state only on first render when auth is not initialized\r\n  if (isLoading.auth) {\r\n    return (\r\n      <LoadingScreen\r\n        message=\"Loading authentication...\"\r\n        showLogo={true}\r\n        showSpinner={true}\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex min-h-screen flex-col items-center justify-center bg-background\">\r\n      <div className=\"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px] p-4\">\r\n        <div className=\"flex flex-col space-y-2 text-center\">\r\n          <div className=\"flex justify-center mb-4\">\r\n            <Logo size=\"lg\" />\r\n          </div>\r\n          <h1 className=\"text-2xl font-semibold tracking-tight\">Sign in</h1>\r\n          <p className=\"text-sm text-muted-foreground\">Access your dashboard</p>\r\n        </div>\r\n        <div className=\"rounded-lg border bg-card p-6 shadow-sm\">\r\n          {children}\r\n        </div>\r\n        <div className=\"text-center text-sm text-muted-foreground\">\r\n          <p>© {new Date().getFullYear()} Simba POS. All rights reserved.</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAIA;AACA;AACA;AARA;;;;;;AAcO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD;IAEX,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iDAAiD;QACjD,iCAAiC;QACjC,gEAAgE;QAChE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;YAC3C,WAAW,QAAQ;QACrB,OAAO;YACL,WAAW,QAAQ;QACrB;QAEA,yDAAyD;QACzD,IAAI,eAAe;YACjB,WAAW,QAAQ;YAEnB,4DAA4D;YAC5D,0DAA0D;YAC1D,IAAI,aAAa;gBACf,iBAAiB;YACnB;QACF;IACA,uDAAuD;IACzD,GAAG;QAAC;QAAe;QAAa,gBAAgB,IAAI;KAAC;IAErD,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,UAAU,IAAI,EAAE;gBAClB,WAAW,QAAQ;gBACnB,iBAAiB;YACnB;QACF,GAAG,OAAO,mCAAmC;QAE7C,OAAO,IAAM,aAAa;IAC1B,uDAAuD;IACzD,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,uEAAuE;IACvE,IAAI,UAAU,IAAI,EAAE;QAClB,qBACE,8OAAC,sJAAA,CAAA,gBAAa;YACZ,SAAQ;YACR,UAAU;YACV,aAAa;;;;;;IAGnB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,MAAK;;;;;;;;;;;sCAEb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAE/C,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;4BAAG,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/hooks/use-forgot-password.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useMutation } from '@tanstack/react-query';\r\nimport { authService } from '@/features/auth/api/auth-service';\r\n\r\nexport interface ForgotPasswordRequest {\r\n  email: string;\r\n}\r\n\r\nexport interface ResetPasswordRequest {\r\n  token: string;\r\n  password: string;\r\n}\r\n\r\nexport function useForgotPassword() {\r\n  return useMutation({\r\n    mutationFn: (data: ForgotPasswordRequest) => authService.forgotPassword(data),\r\n  });\r\n}\r\n\r\nexport function useResetPassword() {\r\n  return useMutation({\r\n    mutationFn: (data: ResetPasswordRequest) => authService.resetPassword(data),\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAcO,SAAS;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAAgC,iJAAA,CAAA,cAAW,CAAC,cAAc,CAAC;IAC1E;AACF;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAA+B,iJAAA,CAAA,cAAW,CAAC,aAAa,CAAC;IACxE;AACF", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/components/forgot-password-form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\r\nimport { AlertCircle, CheckCircle2 } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useForgotPassword } from \"@/features/auth/hooks/use-forgot-password\";\r\n\r\nexport function ForgotPasswordForm() {\r\n  const [email, setEmail] = useState(\"\");\r\n  const forgotPassword = useForgotPassword();\r\n  const [isSubmitted, setIsSubmitted] = useState(false);\r\n\r\n  const handleForgotPassword = async () => {\r\n    try {\r\n      await forgotPassword.mutateAsync({ email });\r\n      setIsSubmitted(true);\r\n    } catch {\r\n      // Error is handled by the mutation\r\n    }\r\n  };\r\n\r\n  if (isSubmitted) {\r\n    return (\r\n      <div className=\"grid gap-6\">\r\n        <Alert className=\"bg-green-50 border-green-200\">\r\n          <CheckCircle2 className=\"h-4 w-4 text-green-600\" />\r\n          <AlertDescription className=\"text-green-700\">\r\n            If an account exists with that email, we&apos;ve sent password reset\r\n            instructions.\r\n          </AlertDescription>\r\n        </Alert>\r\n        <Button asChild variant=\"outline\">\r\n          <Link href=\"/login\">Back to Login</Link>\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"grid gap-6\">\r\n      <div>\r\n        <div className=\"grid gap-4\">\r\n          {forgotPassword.isError && (\r\n            <Alert variant=\"destructive\">\r\n              <AlertCircle className=\"h-4 w-4\" />\r\n              <AlertDescription>\r\n                {(forgotPassword.error as Error)?.message ||\r\n                  \"Something went wrong. Please try again.\"}\r\n              </AlertDescription>\r\n            </Alert>\r\n          )}\r\n\r\n          <div className=\"grid gap-2\">\r\n            <Label htmlFor=\"email\">Email</Label>\r\n            <Input\r\n              id=\"email\"\r\n              type=\"email\"\r\n              placeholder=\"<EMAIL>\"\r\n              value={email}\r\n              onChange={(e) => setEmail(e.target.value)}\r\n              required\r\n              autoComplete=\"email\"\r\n            />\r\n          </div>\r\n\r\n          <Button\r\n            type=\"button\"\r\n            className=\"w-full\"\r\n            disabled={forgotPassword.isPending}\r\n            onClick={handleForgotPassword}\r\n          >\r\n            {forgotPassword.isPending ? \"Sending...\" : \"Send Reset Link\"}\r\n          </Button>\r\n\r\n          <div className=\"text-center text-sm\">\r\n            <Link href=\"/login\" className=\"text-primary hover:underline\">\r\n              Back to Login\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AATA;;;;;;;;;;AAWO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,oBAAiB,AAAD;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,eAAe,WAAW,CAAC;gBAAE;YAAM;YACzC,eAAe;QACjB,EAAE,OAAM;QACN,mCAAmC;QACrC;IACF;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,8OAAC,qNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;sCACxB,8OAAC,iIAAA,CAAA,mBAAgB;4BAAC,WAAU;sCAAiB;;;;;;;;;;;;8BAK/C,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;oBAAC,SAAQ;8BACtB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAS;;;;;;;;;;;;;;;;;IAI5B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;sBACC,cAAA,8OAAC;gBAAI,WAAU;;oBACZ,eAAe,OAAO,kBACrB,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,iIAAA,CAAA,mBAAgB;0CACd,AAAC,eAAe,KAAK,EAAY,WAChC;;;;;;;;;;;;kCAKR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAQ;;;;;;0CACvB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,QAAQ;gCACR,cAAa;;;;;;;;;;;;kCAIjB,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU,eAAe,SAAS;wBAClC,SAAS;kCAER,eAAe,SAAS,GAAG,eAAe;;;;;;kCAG7C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAU;sCAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzE", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/forgot-password/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AuthLayout } from \"@/components/layouts/auth-layout\";\r\nimport { ForgotPasswordForm } from \"@/features/auth/components/forgot-password-form\";\r\n\r\nexport default function ForgotPasswordPage() {\r\n  return (\r\n    <AuthLayout>\r\n      <div className=\"flex flex-col space-y-2 text-center mb-4\">\r\n        <h1 className=\"text-2xl font-semibold tracking-tight\">\r\n          Forgot Password\r\n        </h1>\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Enter your email address and we&apos;ll send you a link to reset your\r\n          password.\r\n        </p>\r\n      </div>\r\n      <ForgotPasswordForm />\r\n    </AuthLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC,+IAAA,CAAA,aAAU;;0BACT,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAK/C,8OAAC,oKAAA,CAAA,qBAAkB;;;;;;;;;;;AAGzB", "debugId": null}}]}