"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Upload, RefreshCw, CheckCircle, AlertCircle } from "lucide-react";
import { useImportPriceList } from "../hooks/use-price-list-excel";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

export function PriceListExcelUpload() {
  const [file, setFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const importPriceList = useImportPriceList();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // Validate file type
      const allowedTypes = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
      ];
      const allowedExtensions = [".xlsx", ".xls"];
      const fileExtension = selectedFile.name.toLowerCase().slice(-5);

      if (
        !allowedTypes.includes(selectedFile.type) &&
        !allowedExtensions.some((ext) => fileExtension.includes(ext))
      ) {
        toast.error("Invalid file type", {
          description: "Please select an Excel file (.xlsx or .xls)",
        });
        return;
      }

      setFile(selectedFile);
      setUploadProgress(0);
    }
  };

  const resetFileInput = () => {
    setFile(null);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error("Please select a file first");
      return;
    }

    // Show a loading toast
    const loadingToastId = toast.loading("Processing price list data...");

    // Simulate progress
    const progressInterval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 200);

    try {
      // Upload the file
      const result = await importPriceList.mutateAsync(file);

      // Complete the progress
      clearInterval(progressInterval);
      setUploadProgress(100);

      // Dismiss the loading toast
      toast.dismiss(loadingToastId);

      // Show success toast with details
      const successCount = result?.success_count || 0;
      const errorCount = result?.error_count || 0;

      if (successCount > 0 && errorCount === 0) {
        toast.success("Price list imported successfully", {
          description: `${successCount} products updated successfully`,
        });
      } else if (successCount > 0 && errorCount > 0) {
        toast.warning("Price list imported with some errors", {
          description: `${successCount} products updated, ${errorCount} errors occurred`,
        });
      } else if (errorCount > 0) {
        toast.error("Price list import failed", {
          description: `${errorCount} errors occurred during import`,
        });
      }

      // Show detailed errors if any
      if (result?.errors && result.errors.length > 0) {
        console.log("Import errors:", result.errors);
      }

      // Reset the form after a delay
      setTimeout(() => {
        // Use the reset function to properly clear the file input
        resetFileInput();

        // Refresh the page to show the updated data
        router.refresh();
      }, 1500);
    } catch (error: any) {
      // Dismiss the loading toast
      toast.dismiss(loadingToastId);

      // Clear the progress
      clearInterval(progressInterval);
      // Don't reset the file input on error so the user can try again with the same file

      // Show error toast
      toast.error("Failed to import price list data", {
        description:
          error.message || "An unexpected error occurred. Please try again.",
      });

      console.error("Error uploading file:", error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Price List Excel File
        </CardTitle>
        <CardDescription>
          Upload an Excel file with product pricing data to update your price list
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Make sure to download and use the template provided above. The file
              should contain valid product IDs and pricing information.
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <Label htmlFor="excel-file">Select Excel File</Label>
            <div className="flex gap-2">
              <Input
                id="excel-file"
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileChange}
                disabled={importPriceList.isPending}
                ref={fileInputRef}
                className="flex-1"
              />
              {file && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={resetFileInput}
                  title="Reset file selection"
                  type="button"
                  disabled={importPriceList.isPending}
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              )}
            </div>
            <div className="flex justify-between">
              <p className="text-sm text-muted-foreground">
                Upload an Excel file (.xlsx, .xls) with pricing data
              </p>
              {file && (
                <p className="text-sm font-medium">
                  {file.name} ({Math.round(file.size / 1024)} KB)
                </p>
              )}
            </div>
          </div>

          {uploadProgress > 0 && uploadProgress < 100 && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Uploading...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          {uploadProgress === 100 && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                File uploaded successfully! Processing complete.
              </AlertDescription>
            </Alert>
          )}

          <Button
            onClick={handleUpload}
            disabled={!file || importPriceList.isPending}
            className="w-full"
          >
            {importPriceList.isPending ? (
              "Processing..."
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" /> Upload and Import
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
