/**
 * Price List Excel Controller
 * Handles HTTP requests for price list Excel operations
 */
const path = require("path");
const fs = require("fs");
const {
  generatePriceListImportTemplate,
  processPriceListImport,
  exportPriceListData,
} = require("../services/price-list-excel.service");
const { Product } = require("../models");
const AppError = require("../utils/error");
const logger = require("../utils/logger");

/**
 * Download price list Excel template
 * GET /api/v1/price-list-excel/template
 */
const downloadTemplate = async (req, res, next) => {
  try {
    const tenantId = req.query.tenant_id || req.user.tenant_id;

    if (!tenantId) {
      return next(new AppError("Tenant ID is required", 400));
    }

    logger.info(`Generating price list template for tenant: ${tenantId}`);

    const buffer = await generatePriceListImportTemplate({ tenantId });

    // Set response headers for file download
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      "attachment; filename=price_list_template.xlsx"
    );
    res.setHeader("Content-Length", buffer.length);

    // Send the file
    res.send(buffer);
  } catch (error) {
    logger.error(`Error in downloadTemplate: ${error.message}`);
    next(error);
  }
};

/**
 * Import price list from Excel file
 * POST /api/v1/price-list-excel/import
 */
const importPriceList = async (req, res, next) => {
  let filePath = null;

  try {
    // Check if file was uploaded
    const file = req.file;
    if (!file) {
      return next(new AppError("No file uploaded", 400));
    }

    filePath = file.path;
    logger.info(`Processing price list import from file: ${file.originalname}`);

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return next(
        new AppError(
          `File size too large: ${Math.round(file.size / 1024 / 1024)}MB. Maximum allowed size is 10MB.`,
          400
        )
      );
    }

    // Check file extension
    const fileExtension = path.extname(file.originalname).toLowerCase();
    if (![".xlsx", ".xls"].includes(fileExtension)) {
      return next(
        new AppError(
          `Invalid file extension: ${fileExtension}. Only .xlsx and .xls files are allowed.`,
          400
        )
      );
    }

    // Check user permissions
    if (!req.user || !req.user.tenant_id) {
      return next(
        new AppError(
          "User authentication or tenant information is missing",
          401
        )
      );
    }

    // Process the import
    const results = await processPriceListImport(file.path, {
      tenantId: req.user.tenant_id,
      userId: req.user.id,
      useFilePath: true,
    });

    // Clean up the temporary file
    fs.unlink(file.path, (err) => {
      if (err) {
        logger.error(`Error deleting temporary file: ${err.message}`);
      }
      filePath = null;
    });

    logger.info(
      `Price list import completed: ${results.success_count} success, ${results.error_count} errors`
    );

    res.status(200).json({
      success: true,
      message: "Price list import completed",
      data: results,
      success_count: results.success_count,
      error_count: results.error_count,
      errors: results.errors,
      warnings: results.warnings,
    });
  } catch (error) {
    // Clean up the temporary file if it exists
    if (filePath && fs.existsSync(filePath)) {
      fs.unlink(filePath, (err) => {
        if (err) {
          logger.error(`Error deleting temporary file: ${err.message}`);
        }
      });
    }

    logger.error(`Error in importPriceList: ${error.message}`);
    next(error);
  }
};

/**
 * Export price list data to Excel
 * GET /api/v1/price-list-excel/export
 */
const exportPriceList = async (req, res, next) => {
  try {
    const tenantId = req.query.tenant_id || req.user.tenant_id;

    if (!tenantId) {
      return next(new AppError("Tenant ID is required", 400));
    }

    logger.info(`Exporting price list data for tenant: ${tenantId}`);

    const buffer = await exportPriceListData({ tenantId });

    // Generate filename with current date
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
    const filename = `price_list_export_${dateStr}.xlsx`;

    // Set response headers for file download
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}`
    );
    res.setHeader("Content-Length", buffer.length);

    // Send the file
    res.send(buffer);
  } catch (error) {
    logger.error(`Error in exportPriceList: ${error.message}`);
    next(error);
  }
};

/**
 * Get product count for export
 * GET /api/v1/price-list-excel/count
 */
const getProductCount = async (req, res, next) => {
  try {
    const tenantId = req.query.tenant_id || req.user.tenant_id;

    if (!tenantId) {
      return next(new AppError("Tenant ID is required", 400));
    }

    const count = await Product.count({
      where: {
        tenant_id: tenantId,
        deleted_at: null,
      },
    });

    res.status(200).json({
      success: true,
      data: { count },
      count,
    });
  } catch (error) {
    logger.error(`Error in getProductCount: ${error.message}`);
    next(error);
  }
};

module.exports = {
  downloadTemplate,
  importPriceList,
  exportPriceList,
  getProductCount,
};
