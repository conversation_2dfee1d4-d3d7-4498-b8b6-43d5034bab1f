"use client";

import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { priceListExcelService } from "../api/price-list-excel-service";
import { useState, useCallback } from "react";

/**
 * Hook for downloading the price list Excel template
 */
export function useDownloadPriceListTemplate() {
  return useMutation({
    mutationFn: (params?: { tenant_id?: number }) =>
      priceListExcelService.downloadTemplate(params),
    onSuccess: () => {
      toast.success("Template downloaded successfully");
    },
    onError: (error: any) => {
      console.error("Error downloading template:", error);
      toast.error("Failed to download template", {
        description: error.message || "Please try again later",
      });
    },
  });
}

/**
 * Hook for importing price list Excel file
 */
export function useImportPriceList() {
  return useMutation({
    mutationFn: (file: File) => priceListExcelService.importPriceList(file),
    onError: (error: any) => {
      console.error("Error importing price list:", error);
      // Don't show toast here as it's handled in the component
    },
  });
}

/**
 * Hook for exporting price list data
 */
export function useExportPriceList() {
  return useMutation({
    mutationFn: (params?: { tenant_id?: number }) =>
      priceListExcelService.exportPriceList(params),
    onSuccess: () => {
      toast.success("Price list exported successfully");
    },
    onError: (error: any) => {
      console.error("Error exporting price list:", error);
      toast.error("Failed to export price list", {
        description: error.message || "Please try again later",
      });
    },
  });
}

/**
 * Hook for getting product count
 */
export function useProductCount() {
  return useQuery({
    queryKey: ["price-list-product-count"],
    queryFn: () => priceListExcelService.getProductCount(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook for exporting all price list data with loading state
 */
export function useExportAllPriceList() {
  const [isLoading, setIsLoading] = useState(false);
  const { data: productCount = 0 } = useProductCount();
  const exportMutation = useExportPriceList();

  const exportAll = useCallback(() => {
    if (isLoading) {
      return false;
    }

    if (productCount === 0) {
      toast.warning("No products to export", {
        description: "Please add products before exporting",
      });
      return false;
    }

    setIsLoading(true);

    exportMutation.mutate(undefined, {
      onSuccess: () => {
        setIsLoading(false);
        toast.success("Price list exported successfully", {
          description: `${productCount} products exported to Excel`,
        });
      },
      onError: (error: any) => {
        setIsLoading(false);
        toast.error("Failed to export price list", {
          description: error.message || "Please try again later",
        });
      },
    });

    return true;
  }, [isLoading, productCount, exportMutation]);

  return {
    exportAll,
    isLoading: isLoading || exportMutation.isPending,
    productCount,
  };
}
