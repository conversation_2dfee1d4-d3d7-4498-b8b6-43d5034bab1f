/**
 * Price List Excel Routes
 * Routes for price list Excel import/export operations
 */
const express = require("express");
const multer = require("multer");
const path = require("path");
const fs = require("fs");
const {
  downloadTemplate,
  importPriceList,
  exportPriceList,
  getProductCount,
} = require("../controllers/price-list-excel.controller");
const { authenticate } = require("../middleware/auth");

const router = express.Router();

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, "../../uploads");
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const extension = path.extname(file.originalname);
    cb(null, `price-list-${uniqueSuffix}${extension}`);
  },
});

const fileFilter = (req, file, cb) => {
  // Check file type
  const allowedMimes = [
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
    "application/vnd.ms-excel", // .xls
  ];

  const allowedExtensions = [".xlsx", ".xls"];
  const fileExtension = path.extname(file.originalname).toLowerCase();

  if (
    allowedMimes.includes(file.mimetype) ||
    allowedExtensions.includes(fileExtension)
  ) {
    cb(null, true);
  } else {
    cb(
      new Error(
        "Invalid file type. Only Excel files (.xlsx, .xls) are allowed."
      ),
      false
    );
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
});

// Apply authentication and tenant validation to all routes
router.use(authenticate);

/**
 * @route   GET /api/v1/price-list-excel/template
 * @desc    Download price list Excel template
 * @access  Private
 * @query   tenant_id (optional) - Tenant ID to filter products
 */
router.get("/template", downloadTemplate);

/**
 * @route   POST /api/v1/price-list-excel/import
 * @desc    Import price list from Excel file
 * @access  Private
 * @body    file - Excel file containing price list data
 */
router.post("/import", upload.single("file"), importPriceList);

/**
 * @route   GET /api/v1/price-list-excel/export
 * @desc    Export current price list data to Excel
 * @access  Private
 * @query   tenant_id (optional) - Tenant ID to filter products
 */
router.get("/export", exportPriceList);

/**
 * @route   GET /api/v1/price-list-excel/count
 * @desc    Get count of products for export
 * @access  Private
 * @query   tenant_id (optional) - Tenant ID to filter products
 */
router.get("/count", getProductCount);

module.exports = router;
