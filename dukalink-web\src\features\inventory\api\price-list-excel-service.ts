import { api } from "@/lib/api";

export interface PriceListImportResult {
  success_count: number;
  error_count: number;
  errors?: Array<{
    row: number;
    message: string;
    data?: any;
  }>;
  warnings?: Array<{
    row: number;
    message: string;
    data?: any;
  }>;
}

export interface PriceListExportData {
  product_id: number;
  product_name: string;
  sku?: string;
  cost_price: number;
  selling_price: number;
  margin: number;
  margin_percentage: number;
}

class PriceListExcelService {
  /**
   * Download the price list Excel template
   */
  async downloadTemplate(params?: { tenant_id?: number }): Promise<void> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.tenant_id) {
        queryParams.append("tenant_id", params.tenant_id.toString());
      }

      const url = `/api/v1/price-list-excel/template${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      const response = await api.get(url, {
        responseType: "blob",
      });

      // Create blob link to download
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = "price_list_template.xlsx";
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error("Error downloading price list template:", error);
      throw error;
    }
  }

  /**
   * Upload and import price list Excel file
   */
  async importPriceList(file: File): Promise<PriceListImportResult> {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await api.post<PriceListImportResult>(
        "/api/v1/price-list-excel/import",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error("Error importing price list:", error);
      throw error;
    }
  }

  /**
   * Export all price list data to Excel
   */
  async exportPriceList(params?: { tenant_id?: number }): Promise<void> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.tenant_id) {
        queryParams.append("tenant_id", params.tenant_id.toString());
      }

      const url = `/api/v1/price-list-excel/export${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      const response = await api.get(url, {
        responseType: "blob",
      });

      // Create blob link to download
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      
      // Generate filename with current date
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
      link.download = `price_list_export_${dateStr}.xlsx`;
      
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error("Error exporting price list:", error);
      throw error;
    }
  }

  /**
   * Get count of products for export
   */
  async getProductCount(params?: { tenant_id?: number }): Promise<number> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.tenant_id) {
        queryParams.append("tenant_id", params.tenant_id.toString());
      }

      const url = `/api/v1/price-list-excel/count${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;

      const response = await api.get<{ count: number }>(url);
      return response.data.count;
    } catch (error) {
      console.error("Error getting product count:", error);
      return 0;
    }
  }
}

export const priceListExcelService = new PriceListExcelService();
