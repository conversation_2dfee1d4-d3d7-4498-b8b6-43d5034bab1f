"use client";

import { MainLayout } from "@/components/layouts/main-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  Bread<PERSON>rumbLink,
  Bread<PERSON>rumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ChevronRight, FileSpreadsheet } from "lucide-react";
import { InventoryTemplateDownload } from "@/features/inventory/components/inventory-template-download";
import { InventoryExcelUpload } from "@/features/inventory/components/inventory-excel-upload";
import { useExportAllInventory } from "@/features/inventory/hooks/use-inventory-excel";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";

export default function InventoryExcelPage() {
  // Use the export hook
  const {
    exportAll,
    isLoading: isExportLoading,
    inventoryCount,
  } = useExportAllInventory();

  // Handle export button click
  const handleExport = () => {
    if (isExportLoading) {
      toast.info("Export is already in progress");
      return;
    }

    if (inventoryCount === 0) {
      toast.warning("No inventory data to export", {
        description: "Please add inventory items before exporting",
      });
      return;
    }

    // Show a loading toast
    const loadingToastId = toast.loading(
      "Preparing inventory data for export..."
    );

    // Start the export process
    const success = exportAll();

    // Dismiss the loading toast
    setTimeout(() => {
      toast.dismiss(loadingToastId);

      if (success) {
        toast.success("Inventory exported successfully", {
          description: `${inventoryCount} items exported to Excel`,
        });
      }
    }, 1000);
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/inventory">Inventory</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbLink>Inventory Excel Import/Export</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Inventory Excel Import/Export
          </h1>
          <p className="text-muted-foreground">
            Import and export inventory data using Excel files
          </p>
        </div>

        <Tabs defaultValue="import" className="space-y-4">
          <TabsList>
            <TabsTrigger value="import">Import Inventory</TabsTrigger>
            <TabsTrigger value="export">Export Inventory</TabsTrigger>
          </TabsList>

          <TabsContent value="import" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Import Inventory from Excel</CardTitle>
                <CardDescription>
                  Upload an Excel file to update inventory data in bulk
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4">
                  Use this feature to import inventory data from an Excel file.
                  The file should follow the template format.
                </p>

                <InventoryTemplateDownload />
                <div className="mt-8">
                  <InventoryExcelUpload />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Export Inventory to Excel</CardTitle>
                <CardDescription>
                  Download inventory data as an Excel file
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4">
                  Use this feature to export your inventory data to an Excel
                  file for analysis or record-keeping.
                </p>

                <div className="space-y-4">
                  <p>
                    You can export your current inventory data to an Excel file.
                    This will include all inventory items with their details
                    such as product name, SKU, barcode, branch, quantity, and
                    more.
                  </p>

                  {inventoryCount > 0 && (
                    <p className="text-sm text-muted-foreground">
                      {inventoryCount} items available for export
                    </p>
                  )}

                  <div className="flex justify-center">
                    <Button
                      onClick={handleExport}
                      disabled={isExportLoading}
                      className="w-full sm:w-auto"
                    >
                      {isExportLoading ? (
                        "Exporting..."
                      ) : (
                        <>
                          <FileSpreadsheet className="mr-2 h-4 w-4" /> Export
                          Inventory to Excel
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
