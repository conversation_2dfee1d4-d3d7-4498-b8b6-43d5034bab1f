"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Download, FileSpreadsheet, Info } from "lucide-react";
import { useDownloadPriceListTemplate } from "../hooks/use-price-list-excel";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";

export function PriceListTemplateDownload() {
  const [showInstructions, setShowInstructions] = useState(false);
  const downloadTemplate = useDownloadPriceListTemplate();

  const handleDownload = () => {
    // Show a loading toast
    const loadingToastId = toast.loading("Preparing template for download...");

    downloadTemplate.mutate(undefined, {
      onSuccess: () => {
        // Dismiss the loading toast
        toast.dismiss(loadingToastId);

        // Show success toast
        toast.success("Template downloaded successfully", {
          description: "You can now fill it with your pricing data",
        });
      },
      onError: (error: any) => {
        // Dismiss the loading toast
        toast.dismiss(loadingToastId);

        // Show error toast
        toast.error("Failed to download template", {
          description: error.message || "Please try again later",
        });
      },
    });
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Download Price List Template
          </CardTitle>
          <CardDescription>
            Download the Excel template to import product pricing data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            Download the Excel template with product dropdowns. Select products
            from the dropdown, enter cost prices and selling prices, then upload
            the file to update your product pricing in bulk.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <Button
              onClick={handleDownload}
              disabled={downloadTemplate.isPending}
              className="w-full sm:w-auto"
            >
              {downloadTemplate.isPending ? (
                "Downloading..."
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" /> Download Template
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={() => setShowInstructions(!showInstructions)}
              className="w-full sm:w-auto"
            >
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              {showInstructions ? "Hide Instructions" : "View Instructions"}
            </Button>
          </div>

          {showInstructions && (
            <Alert className="mt-4">
              <Info className="h-4 w-4" />
              <AlertTitle>Template Instructions</AlertTitle>
              <AlertDescription className="mt-2 space-y-2">
                <div>
                  <strong>Template Structure:</strong>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>
                      <strong>Product:</strong> Select from dropdown list (required) - contains product name, SKU, and ID
                    </li>
                    <li>
                      <strong>Cost Price:</strong> The buying/cost price of the product (required)
                    </li>
                    <li>
                      <strong>Selling Price:</strong> The selling price of the product (required)
                    </li>
                  </ul>
                  <p className="mt-2 text-sm text-muted-foreground">
                    <strong>Note:</strong> Margin and margin percentage will be calculated automatically by the system using the formula: ((Selling Price - Cost Price) / Cost Price) × 100
                  </p>
                </div>
                <div>
                  <strong>Important Notes:</strong>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Select products from the dropdown - do not type manually</li>
                    <li>Cost Price and Selling Price are required fields</li>
                    <li>Margin and margin percentage will be calculated automatically</li>
                    <li>Save the file in Excel format (.xlsx or .xls) before uploading</li>
                    <li>Empty rows will be ignored during import</li>
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
